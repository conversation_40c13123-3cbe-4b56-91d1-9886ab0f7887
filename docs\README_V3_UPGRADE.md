# Microsoft Registration System v3.0 Upgrade Guide

## Overview

The Microsoft Registration System has been completely rewritten to v3.0 with a state machine-based staged processing architecture. This provides more reliable, intelligent registration flow management.

## Quick Start

### Method 1: One-Click Upgrade (Recommended)
```bash
# Run the one-click upgrade script
one-click-upgrade.bat
```

### Method 2: Manual Steps
```bash
# 1. Check file integrity
check-v3-files.bat

# 2. Configure environment
copy .env.example .env
# Edit .env file with correct IMAP configuration

# 3. Start IMAP server
start-v3-system.bat

# 4. Reload browser extension
# Visit chrome://extensions/ and reload RTBS extension
```

## New Features in v3.0

### State Machine Architecture
- ✅ Strict stage control ensuring effective execution and closure of each stage
- ✅ Automatic resource management and cleanup
- ✅ Intelligent state transitions and validation

### Smart Error Handling
- ✅ Differentiated retry strategies based on error types
- ✅ Automatic failure recovery mechanisms
- ✅ Detailed error analysis and statistics

### Enhanced IMAP Service
- ✅ Connection pool management to avoid connection conflicts
- ✅ Email relevance scoring system
- ✅ Intelligent verification code extraction and caching

### Improved User Interface
- ✅ Real-time status display and version badges
- ✅ Detailed stage and error information
- ✅ Enhanced visual feedback

## File Structure

### Core Files (Updated)
- `manifest.json` - Updated to v3.0.0
- `content.js` - Integrated v3 system with backward compatibility
- `popup.js` - Supports v3 status display and version badges
- `popup.html` - Added v3 styles and version badges
- `imap-server.js` - Updated to v3.0.0
- `.env.example` - Updated configuration template

### New v3.0 System Files
- `microsoft-registration-state-machine.js` - State machine core
- `microsoft-registration-handlers.js` - Main stage handlers
- `remaining-stage-handlers.js` - Remaining stage handlers
- `page-detector.js` - Intelligent page detector
- `microsoft-registration-controller.js` - System controller
- `error-handling-system.js` - Error handling system
- `microsoft-registration-v3.js` - System integration
- `enhanced-imap-service.js` - Enhanced IMAP service

### Tools and Documentation
- `DEPLOYMENT_GUIDE_V3.md` - Detailed deployment guide
- `test-v3-system.js` - Automated test script
- `UPGRADE_SUMMARY.md` - Upgrade summary document
- `one-click-upgrade.bat` - One-click upgrade script
- `check-v3-files.bat` - File check script
- `start-v3-system.bat` - Quick start script

## Configuration

### IMAP Server Configuration
Create `.env` file with the following configuration:

```env
# IMAP Server Configuration
IMAP_HOST=imap.s4464.cfd
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-actual-password
IMAP_TLS=true

# TLS Configuration
TLS_REJECT_UNAUTHORIZED=false
TLS_MIN_VERSION=TLSv1.2

# HTTP Server Configuration
SERVER_PORT=3000

# Debug Options
DEBUG=false
LOG_LEVEL=info

# Email Processing Configuration
MAX_EMAIL_AGE_MINUTES=10
VERIFICATION_CODE_TTL_MINUTES=5
MAX_CONCURRENT_CONNECTIONS=3

# Retry Configuration
MAX_RETRIES=5
RETRY_DELAY_MS=3000
CONNECTION_TIMEOUT_MS=30000
```

## Testing and Verification

### 1. Check System Status
```javascript
// Run in browser Console
const system = getMicrosoftRegistrationSystemV3();
console.log('System Status:', system.getSystemStatus());
```

### 2. Run Automated Tests
```javascript
// Run in browser Console
const testSuite = new V3SystemTestSuite();
testSuite.runAllTests();
```

### 3. Check IMAP Server
```bash
# Check server health
curl http://localhost:3000/health

# View server configuration
curl http://localhost:3000/config
```

## Compatibility

### Backward Compatibility
- If v3 system fails to load, automatically falls back to v2.x compatibility mode
- Maintains original popup interface display
- Preserves basic registration functionality

### Smooth Upgrade
- Old version states automatically sync to v3 system
- Supports progressive feature migration
- Provides complete rollback mechanism

## Troubleshooting

### Common Issues

#### 1. v3 System Not Loading
**Symptoms**: Console shows "Microsoft Registration System v3.0 not loaded"
**Solutions**:
- Check if all v3 files are correctly placed
- Confirm manifest.json is updated
- Reload browser extension

#### 2. IMAP Service Connection Failed
**Symptoms**: Verification code retrieval fails
**Solutions**:
- Check if .env configuration is correct
- Confirm IMAP server is running
- Verify network connection

#### 3. Status Display Abnormal
**Symptoms**: Popup shows errors or doesn't update
**Solutions**:
- Refresh page to reinitialize
- Check Console error logs
- Restart IMAP server

### Get Help
- View detailed documentation: `DEPLOYMENT_GUIDE_V3.md`
- Run test script: `test-v3-system.js`
- Check system logs: Browser Console + IMAP server logs

## Performance Monitoring

### Key Metrics
1. **System Initialization Time**: Should complete within 2 seconds
2. **Page Detection Response Time**: Should complete within 1 second
3. **State Transition Time**: Should complete within 3 seconds
4. **IMAP Response Time**: Should get verification code within 10 seconds
5. **Memory Usage**: Should be controlled within 50MB

### Monitoring Commands
```bash
# Check IMAP server status
curl http://localhost:3000/health

# Check server configuration
curl http://localhost:3000/config
```

## Upgrade Checklist

- [ ] All v3 files correctly placed
- [ ] manifest.json updated
- [ ] IMAP server configured and running
- [ ] Browser extension reloaded
- [ ] System initialization test passed
- [ ] Page detection test passed
- [ ] IMAP service test passed
- [ ] Complete registration flow test passed
- [ ] Error handling test passed
- [ ] Performance metrics meet requirements

## Support

For technical support and detailed documentation, please refer to:
- **Upgrade Summary**: `UPGRADE_SUMMARY.md`
- **Deployment Guide**: `DEPLOYMENT_GUIDE_V3.md`
- **Test Script**: `test-v3-system.js`
- **Configuration Template**: `.env.example`

---

**Version**: Microsoft Registration System v3.0.0  
**Upgrade Date**: January 2024  
**Technical Support**: See DEPLOYMENT_GUIDE_V3.md
