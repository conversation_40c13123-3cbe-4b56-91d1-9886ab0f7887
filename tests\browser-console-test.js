/**
 * 浏览器控制台验证脚本
 * 在Microsoft注册页面的浏览器控制台中运行此脚本来验证修复效果
 * 
 * 使用方法：
 * 1. 打开Microsoft注册页面
 * 2. 按F12打开开发者工具
 * 3. 切换到Console标签
 * 4. 复制并粘贴此脚本，然后按回车运行
 */

console.log('🧪 开始验证undefined属性访问错误修复...\n');

// 测试1: 验证mapV3StageToOldStep函数
function testMapV3StageToOldStepInBrowser() {
  console.log('🔍 测试1: mapV3StageToOldStep函数');
  
  if (typeof mapV3StageToOldStep === 'function') {
    try {
      // 测试undefined输入
      const result1 = mapV3StageToOldStep(undefined);
      console.log(`✅ undefined输入: ${result1}`);
      
      // 测试null输入
      const result2 = mapV3StageToOldStep(null);
      console.log(`✅ null输入: ${result2}`);
      
      // 测试正常输入
      const result3 = mapV3StageToOldStep('idle');
      console.log(`✅ 正常输入: ${result3}`);
      
      return true;
    } catch (error) {
      console.error(`❌ mapV3StageToOldStep测试失败: ${error.message}`);
      return false;
    }
  } else {
    console.log('⚠️ mapV3StageToOldStep函数不存在，可能页面未完全加载');
    return false;
  }
}

// 测试2: 验证v3系统状态获取
function testV3SystemStatusInBrowser() {
  console.log('\n🔍 测试2: v3系统状态获取');
  
  if (typeof registrationSystemV3 !== 'undefined' && registrationSystemV3) {
    try {
      const status = registrationSystemV3.getSystemStatus();
      console.log('✅ v3系统状态获取成功:', status);
      
      // 检查控制器状态
      if (status.controller) {
        console.log('✅ 控制器状态存在:', status.controller);
        
        // 检查currentStage是否安全
        const currentStage = status.controller.currentStage;
        console.log(`✅ 当前阶段: ${currentStage || 'undefined/null'}`);
      }
      
      return true;
    } catch (error) {
      console.error(`❌ v3系统状态获取失败: ${error.message}`);
      return false;
    }
  } else {
    console.log('⚠️ registrationSystemV3不存在，可能使用兼容模式');
    return true; // 这不是错误，只是使用不同的模式
  }
}

// 测试3: 验证状态同步函数
function testStateSyncInBrowser() {
  console.log('\n🔍 测试3: 状态同步函数');
  
  if (typeof syncStateFromV3 === 'function') {
    try {
      syncStateFromV3();
      console.log('✅ 状态同步函数执行成功');
      return true;
    } catch (error) {
      console.error(`❌ 状态同步失败: ${error.message}`);
      return false;
    }
  } else {
    console.log('⚠️ syncStateFromV3函数不存在');
    return false;
  }
}

// 测试4: 验证Microsoft注册状态获取
function testMicrosoftStatusInBrowser() {
  console.log('\n🔍 测试4: Microsoft注册状态获取');
  
  if (typeof getMicrosoftRegistrationStatus === 'function') {
    try {
      const status = getMicrosoftRegistrationStatus();
      console.log('✅ Microsoft注册状态获取成功:', status);
      
      // 检查关键属性
      console.log(`✅ 当前步骤: ${status.currentStep || 'undefined'}`);
      console.log(`✅ 是否处理中: ${status.isProcessing}`);
      console.log(`✅ 系统版本: ${status.systemVersion}`);
      
      return true;
    } catch (error) {
      console.error(`❌ Microsoft注册状态获取失败: ${error.message}`);
      return false;
    }
  } else {
    console.log('⚠️ getMicrosoftRegistrationStatus函数不存在');
    return false;
  }
}

// 测试5: 模拟undefined属性访问错误
function testUndefinedPropertyAccess() {
  console.log('\n🔍 测试5: 模拟undefined属性访问');
  
  try {
    // 模拟可能导致错误的情况
    const testObj = undefined;
    
    // 这应该不会导致错误，因为我们的修复应该处理这种情况
    if (typeof mapV3StageToOldStep === 'function') {
      const result = mapV3StageToOldStep(testObj?.currentStage);
      console.log(`✅ 模拟undefined属性访问处理成功: ${result}`);
    }
    
    return true;
  } catch (error) {
    console.error(`❌ undefined属性访问测试失败: ${error.message}`);
    return false;
  }
}

// 测试6: 验证阶段转换函数
function testStageTransitionInBrowser() {
  console.log('\n🔍 测试6: 阶段转换函数');

  if (typeof isValidStageTransition === 'function') {
    try {
      // 测试undefined参数
      const result1 = isValidStageTransition(undefined, 'signup');
      console.log(`✅ undefined fromStage处理: ${result1}`);

      // 测试null参数
      const result2 = isValidStageTransition('none', null);
      console.log(`✅ null toStage处理: ${result2}`);

      // 测试正常转换
      const result3 = isValidStageTransition('none', 'signup');
      console.log(`✅ 正常转换测试: ${result3}`);

      return true;
    } catch (error) {
      console.error(`❌ 阶段转换函数测试失败: ${error.message}`);
      return false;
    }
  } else {
    console.log('⚠️ isValidStageTransition函数不存在');
    return false;
  }
}

// 测试7: 验证阶段处理判断函数
function testShouldProcessStageInBrowser() {
  console.log('\n🔍 测试7: 阶段处理判断函数');

  if (typeof shouldProcessStage === 'function') {
    try {
      // 测试undefined参数
      const result1 = shouldProcessStage(undefined);
      console.log(`✅ undefined参数处理: ${result1}`);

      // 测试null参数
      const result2 = shouldProcessStage(null);
      console.log(`✅ null参数处理: ${result2}`);

      // 测试正常参数
      const result3 = shouldProcessStage('signup');
      console.log(`✅ 正常参数处理: ${result3}`);

      return true;
    } catch (error) {
      console.error(`❌ 阶段处理判断函数测试失败: ${error.message}`);
      return false;
    }
  } else {
    console.log('⚠️ shouldProcessStage函数不存在');
    return false;
  }
}

// 运行所有测试
function runBrowserTests() {
  console.log('🚀 开始在浏览器中验证修复效果...\n');

  const tests = [
    { name: 'mapV3StageToOldStep函数', test: testMapV3StageToOldStepInBrowser },
    { name: 'v3系统状态获取', test: testV3SystemStatusInBrowser },
    { name: '状态同步函数', test: testStateSyncInBrowser },
    { name: 'Microsoft注册状态获取', test: testMicrosoftStatusInBrowser },
    { name: 'undefined属性访问处理', test: testUndefinedPropertyAccess },
    { name: '阶段转换函数', test: testStageTransitionInBrowser },
    { name: '阶段处理判断函数', test: testShouldProcessStageInBrowser }
  ];
  
  let passed = 0;
  let failed = 0;
  
  tests.forEach(({ name, test }) => {
    try {
      if (test()) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ 测试"${name}"执行异常: ${error.message}`);
      failed++;
    }
  });
  
  console.log('\n📋 浏览器验证结果:');
  console.log(`✅ 通过: ${passed}`);
  console.log(`❌ 失败: ${failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 所有测试通过！undefined属性访问错误修复验证成功！');
    console.log('💡 建议：如果之前遇到错误，请刷新页面重新测试Microsoft注册功能。');
  } else {
    console.log('\n⚠️ 部分测试失败，可能需要进一步检查或页面未完全加载。');
    console.log('💡 建议：刷新页面后重新运行此测试。');
  }
  
  return failed === 0;
}

// 自动运行测试
runBrowserTests();

// 提供手动运行选项
console.log('\n💡 如需重新运行测试，请在控制台中输入: runBrowserTests()');
