# Microsoft注册账号清除修复说明

## 🎯 问题描述

在Microsoft账户注册时，面板总是会先显示之前的账号，这是不正确的行为。用户期望：
- **新注册流程**：应该清除之前的账号信息，生成新的账号
- **邮箱验证登录**：应该保留现有账号信息，无需清除

## 🔧 修复方案

### 1. 增强页面检测逻辑

**新增函数：**
- `isNewRegistrationStart()` - 检测是否为全新的注册流程开始页面
- `isEmailVerificationLogin()` - 检测是否为邮箱验证登录场景

**检测逻辑：**
```javascript
// 检测全新注册流程
function isNewRegistrationStart() {
  const isSignupUrl = currentUrl.includes('signup.live.com/signup');
  const hasNewAccountKeywords = bodyText.includes('创建账户') || 
                               bodyText.includes('Create account');
  const hasEmailInput = document.querySelector('input[type="email"]') !== null;
  
  return isSignupUrl && hasNewAccountKeywords && hasEmailInput;
}

// 检测邮箱验证登录
function isEmailVerificationLogin() {
  const hasLoginKeywords = bodyText.includes('登录') || 
                          bodyText.includes('验证你的电子邮件');
  const hasLoginUrl = currentUrl.includes('login.live.com') || 
                     currentUrl.includes('oauth20_authorize');
  
  return hasLoginKeywords && hasLoginUrl;
}
```

### 2. 智能状态清除机制

**修改 `handleMicrosoftSignupPage()` 函数：**
```javascript
// 检查是否为全新的注册流程开始
const isNewRegistrationStartPage = isNewRegistrationStart();
const hasExistingAccount = msRegistrationState.currentAccount !== null;
const isLoginScenario = isEmailVerificationLogin();

// 只有在全新注册流程且不是登录场景时才清除
if (isNewRegistrationStartPage && hasExistingAccount && !isLoginScenario) {
  console.log('🔄 检测到全新的注册流程开始，清除之前的账号状态');
  
  // 重置注册状态
  msRegistrationState.currentAccount = null;
  msRegistrationState.currentStep = 'none';
  msRegistrationState.attempts = 0;
  msRegistrationState.verificationStatus = '等待中';
  msRegistrationState.registrationComplete = false;
  
  // 清除本地存储
  chrome.storage.local.remove(['microsoft_registration_status']);
}
```

### 3. Popup与Content Script通信

**在 `popup.js` 中添加检查机制：**
```javascript
// 检查当前页面是否为全新的注册流程开始页面
const isNewRegistrationPage = tab && tab.url && 
  tab.url.includes('signup.live.com/signup') && 
  !tab.url.includes('oauth20_authorize');

if (isNewRegistrationPage && status.currentAccount) {
  // 向content script发送消息，让其检查是否需要清除状态
  await chrome.tabs.sendMessage(tab.id, { action: 'checkNewRegistration' });
}
```

**在 `content.js` 中添加消息监听：**
```javascript
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'checkNewRegistration') {
    // 执行相同的检查逻辑
    const isNewRegistrationStartPage = isNewRegistrationStart();
    const hasExistingAccount = msRegistrationState.currentAccount !== null;
    const isLoginScenario = isEmailVerificationLogin();
    
    if (isNewRegistrationStartPage && hasExistingAccount && !isLoginScenario) {
      // 清除状态
      clearMicrosoftRegistrationData();
    }
  }
});
```

## 🎯 修复效果

### 修复前的问题：
- ❌ 新注册时显示之前的账号
- ❌ 无法区分新注册和邮箱验证登录
- ❌ 状态清除逻辑不准确

### 修复后的改进：
- ✅ 新注册流程自动清除之前的账号
- ✅ 邮箱验证登录时保留现有账号
- ✅ 精确的页面场景检测
- ✅ 双重检查机制（content script + popup）

## 🧪 测试验证

创建了 `test-account-clear-fix.html` 测试页面，包含：

1. **场景1：全新注册流程**
   - 模拟用户访问Microsoft注册页面
   - 验证是否正确清除之前的账号

2. **场景2：邮箱验证登录**
   - 模拟用户进行邮箱验证登录
   - 验证是否保留现有账号

3. **场景3：继续现有注册流程**
   - 模拟用户在注册流程中的其他步骤
   - 验证是否保留现有账号

## 📋 使用说明

### 新注册流程：
1. 访问Microsoft注册页面
2. 系统自动检测为新注册流程
3. 清除之前的账号信息
4. 生成新的随机账号
5. 开始注册流程

### 邮箱验证登录：
1. 在邮箱验证登录页面
2. 系统检测为登录场景
3. 保留现有账号信息
4. 继续验证流程

## 🔍 调试信息

修复后的代码包含详细的调试日志：
- `🔄 检测到全新的注册流程开始，清除之前的账号状态`
- `📧 检测到邮箱验证登录场景，保留现有账号`
- `📋 继续使用现有账号进行注册流程`

可以通过浏览器开发者工具的Console查看详细的执行日志。

## 🚀 部署说明

修复涉及的文件：
- `content.js` - 主要修复逻辑
- `popup.js` - 辅助检查机制
- `test-account-clear-fix.html` - 测试验证页面

修复完成后，重新加载扩展即可生效。
