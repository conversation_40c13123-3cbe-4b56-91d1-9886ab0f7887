# 微软人机验证修复指南

## 问题描述

在微软注册过程中，遇到人机验证页面时需要长按按钮3秒完成验证，但是扩展程序卡住无法继续。

### 问题原因

1. **选择器过时**: 代码中使用的HTML选择器与当前微软页面的实际结构不匹配
2. **事件模拟不完整**: 长按模拟只使用了基本的mouse事件，可能不足以触发验证
3. **检测逻辑不够健壮**: 页面检测逻辑过于依赖特定的HTML结构

## 修复方案

### 1. 更新选择器

**旧选择器:**
```javascript
// 检测"按住"文本
const holdText = document.querySelector('#xuOftMYuUTBFSOX');

// 查找验证按钮  
const captchaButton = document.querySelector('#ZKlNqxJLSWbMvar');
```

**新选择器:**
```javascript
// 检测"按住"文本 - 多重备选
const holdText = document.querySelector('#GlAInHXoXszrxIm, p:contains("按住"), [class*="按住"]');

// 查找验证按钮 - 多重备选
let captchaButton = document.querySelector('#mWLJzzCQbwTPipB');
if (!captchaButton) {
  captchaButton = document.querySelector('[role="button"][aria-label*="按住"], [role="button"][aria-label*="Human Challenge"], div[tabindex="0"][role="button"]');
}
if (!captchaButton) {
  captchaButton = document.querySelector('.MQfNxwqBJrxZGRt, [class*="MQfNxwqBJrxZGRt"]');
}
```

### 2. 改进长按模拟

**增强的事件模拟:**
- MouseEvent (mousedown/mouseup)
- PointerEvent (pointerdown/pointerup) 
- TouchEvent (touchstart/touchend)
- 精确的坐标计算
- 元素聚焦和滚动到视图

### 3. 强化页面检测

**多重检测机制:**
```javascript
function isCaptchaPage() {
  // 1. 检查标题
  const titleElement = document.querySelector('h1[data-testid="title"]');
  if (titleElement && titleElement.textContent.includes('证明你不是机器人')) {
    return true;
  }
  
  // 2. 检查"按住"文本
  const holdText = document.querySelector('#GlAInHXoXszrxIm, p');
  if (holdText && holdText.textContent.includes('按住')) {
    return true;
  }
  
  // 3. 检查Human Challenge文本
  const challengeText = document.querySelector('[class*="KuFnlXkinpAhhMm"], span');
  if (challengeText && challengeText.textContent.includes('Human Challenge')) {
    return true;
  }
  
  // 4. 检查验证按钮
  const captchaButton = document.querySelector('#mWLJzzCQbwTPipB, [role="button"][aria-label*="按住"]');
  if (captchaButton) {
    return true;
  }
  
  return false;
}
```

## 当前HTML结构

根据提供的HTML，微软人机验证页面的结构如下：

```html
<div id="mWLJzzCQbwTPipB" class="MQfNxwqBJrxZGRt" tabindex="0" 
     aria-describedby="xxblTKsRxFpuOmo VHbHAwjwORfSOXQ" 
     role="button" aria-label="按住 人工挑战">
  <div id="OjMkPZwRnpPfiIV"></div>
  <div id="OpbKnEKGuLyrUUx" dir="auto">
    <div id="qSBcwKvIQomabms"></div>
    <div id="SIbCeleFwbmvutd">
      <p id="GlAInHXoXszrxIm" class="lvKzpRLTBpRNUZg">按住</p>
      <span id="xxblTKsRxFpuOmo" class="KuFnlXkinpAhhMm">
        Human Challenge需要验证。请按住按钮直到验证完成
      </span>
    </div>
  </div>
</div>
```

**关键元素:**
- 按钮ID: `mWLJzzCQbwTPipB`
- 按钮类名: `MQfNxwqBJrxZGRt`
- "按住"文本ID: `GlAInHXoXszrxIm`
- 说明文本ID: `xxblTKsRxFpuOmo`

## 测试方法

### 1. 运行测试页面
```bash
test-captcha-fix.bat
```

### 2. 手动测试步骤
1. 打开测试页面 `test-captcha-fix.html`
2. 点击"测试验证码检测"按钮
3. 点击"测试长按模拟"按钮
4. 查看日志输出确认功能正常

### 3. 实际环境测试
1. 加载修复后的扩展
2. 访问微软注册页面
3. 进行到人机验证步骤
4. 观察扩展是否能自动处理长按验证

## 修复文件

以下文件已被修改:

1. **content.js**
   - `handleCaptchaPage()` - 更新选择器和错误处理
   - `simulateLongPress()` - 增强事件模拟
   - `isCaptchaPage()` - 改进页面检测

2. **test-captcha-fix.html** - 新增测试页面
3. **test-captcha-fix.bat** - 新增测试脚本

## 预期结果

修复后，扩展应该能够:
1. 正确检测到微软人机验证页面
2. 找到验证按钮和相关文本
3. 成功模拟3秒长按操作
4. 完成验证并继续注册流程

## 故障排除

如果修复后仍有问题:

1. **检查控制台日志**: 查看是否有错误信息
2. **验证选择器**: 确认页面HTML结构是否又有变化
3. **测试事件**: 使用测试页面验证事件模拟是否正常
4. **网络延迟**: 增加等待时间以应对网络延迟

## 注意事项

- 微软可能会不定期更新页面结构，需要相应更新选择器
- 人机验证的行为可能因地区和账户状态而异
- 建议定期测试以确保功能正常
