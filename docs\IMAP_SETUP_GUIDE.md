# RTBS IMAP服务器配置指南

## 🎯 概述

这个指南将帮助您配置真实的IMAP邮件服务，用于Microsoft账号注册时自动获取验证码。

## 📋 前置要求

### 1. 系统要求
- Node.js 14.0+ 
- npm 或 yarn
- 稳定的网络连接
- s4464.cfd域名的邮件服务访问权限

### 2. 邮件服务要求
- s4464.cfd域名的IMAP服务器
- 邮箱账号：<EMAIL>
- IMAP服务器地址：imap.s4464.cfd
- IMAP端口：993 (SSL/TLS)

## 🚀 快速安装

### 第1步：安装依赖
```bash
# 进入项目目录
cd RTBS

# 安装Node.js依赖
npm install
```

### 第2步：配置IMAP服务
编辑 `imap-config.json` 文件：
```json
{
  "user": "<EMAIL>",
  "password": "your-actual-password",
  "host": "imap.s4464.cfd",
  "port": 993,
  "tls": true,
  "tlsOptions": {
    "rejectUnauthorized": false
  }
}
```

### 第3步：启动IMAP服务器
```bash
# 启动服务器
npm start

# 或者开发模式（自动重启）
npm run dev
```

### 第4步：测试服务
```bash
# 运行测试脚本
npm test
```

## ⚙️ 详细配置

### IMAP服务器配置

#### 配置文件说明
- `user`: 邮箱账号（<EMAIL>）
- `password`: 邮箱密码（需要真实密码）
- `host`: IMAP服务器地址
- `port`: IMAP端口（通常是993）
- `tls`: 是否使用TLS加密
- `tlsOptions`: TLS选项配置

#### 安全配置
```json
{
  "user": "<EMAIL>",
  "password": "your-secure-password",
  "host": "imap.s4464.cfd",
  "port": 993,
  "tls": true,
  "tlsOptions": {
    "rejectUnauthorized": true,
    "minVersion": "TLSv1.2"
  }
}
```

### 邮件服务器设置

#### 如果您使用cPanel
1. 登录cPanel控制面板
2. 找到"邮件账户"或"Email Accounts"
3. 创建邮箱：<EMAIL>
4. 设置强密码
5. 确保IMAP服务已启用

#### 如果您使用其他邮件服务
1. 确保s4464.cfd域名指向您的邮件服务器
2. 配置MX记录指向邮件服务器
3. 创建*****************邮箱账号
4. 启用IMAP服务（端口993）
5. 配置SSL/TLS证书

## 🔧 服务器API

### 启动后可用的API端点

#### 1. 健康检查
```
GET http://localhost:3000/health
```
返回服务器状态信息

#### 2. 获取验证码
```
GET http://localhost:3000/verification-code/账号@s4464.cfd
```
返回指定邮箱的最新验证码

#### 3. 手动检查邮件
```
POST http://localhost:3000/check-email/账号@s4464.cfd
```
手动触发邮件检查

#### 4. 获取邮件列表
```
GET http://localhost:3000/emails/账号@s4464.cfd
```
获取指定邮箱的邮件列表

#### 5. 配置信息
```
GET http://localhost:3000/config
```
获取当前IMAP配置信息

## 🧪 测试验证

### 1. 基础连接测试
```bash
# 测试服务器是否正常启动
curl http://localhost:3000/health
```

### 2. IMAP连接测试
```bash
# 测试IMAP连接
curl -X POST http://localhost:3000/check-email/<EMAIL>
```

### 3. 完整功能测试
```bash
# 运行完整测试套件
npm test
```

### 4. 手动测试流程
1. 启动IMAP服务器
2. 发送测试邮件到*****************
3. 邮件内容包含6位验证码（如：123456）
4. 调用API获取验证码
5. 验证返回的验证码是否正确

## 🔒 安全注意事项

### 1. 密码安全
- 使用强密码
- 定期更换密码
- 不要在代码中硬编码密码
- 使用环境变量存储敏感信息

### 2. 网络安全
- 仅在本地网络运行服务器
- 不要将服务器暴露到公网
- 使用防火墙限制访问
- 定期更新依赖包

### 3. 邮件安全
- 限制邮箱访问权限
- 定期清理邮件
- 监控异常登录
- 启用两步验证（如果支持）

## 🐛 故障排除

### 常见问题

#### 1. IMAP连接失败
**错误**: `Error: connect ECONNREFUSED`
**解决方案**:
- 检查IMAP服务器地址和端口
- 确认网络连接正常
- 验证防火墙设置
- 检查SSL/TLS配置

#### 2. 认证失败
**错误**: `Error: Invalid credentials`
**解决方案**:
- 验证邮箱账号和密码
- 检查邮箱是否存在
- 确认IMAP服务已启用
- 尝试在邮件客户端测试登录

#### 3. 验证码提取失败
**错误**: 找不到验证码
**解决方案**:
- 检查邮件内容格式
- 验证正则表达式匹配
- 确认邮件是否为最新
- 检查邮件是否被标记为已读

#### 4. 服务器启动失败
**错误**: `Error: listen EADDRINUSE`
**解决方案**:
- 检查端口3000是否被占用
- 更改服务器端口
- 关闭占用端口的程序
- 重启系统

### 调试模式

#### 启用详细日志
```bash
# 设置调试环境变量
export DEBUG=imap:*
npm start
```

#### 查看实时日志
```bash
# 使用nodemon自动重启
npm run dev
```

## 📊 性能优化

### 1. 邮件检查频率
- 默认每3秒检查一次
- 可根据需要调整检查间隔
- 避免过于频繁的检查

### 2. 缓存策略
- 验证码缓存5分钟
- 邮件内容缓存1小时
- 定期清理过期缓存

### 3. 连接池
- 复用IMAP连接
- 限制并发连接数
- 合理设置超时时间

## 🔄 生产环境部署

### 1. 使用PM2管理进程
```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start imap-server.js --name rtbs-imap

# 设置开机自启
pm2 startup
pm2 save
```

### 2. 使用Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 3. 反向代理配置
```nginx
server {
    listen 80;
    server_name imap.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📞 技术支持

### 获取帮助
1. 查看服务器日志
2. 运行测试脚本
3. 检查配置文件
4. 验证网络连接

### 联系支持
- 查看GitHub Issues
- 提交Bug报告
- 请求功能增强

## ✅ 配置检查清单

部署前请确认以下项目：

- [ ] Node.js 14.0+ 已安装
- [ ] npm依赖已安装
- [ ] imap-config.json已正确配置
- [ ] 邮箱*****************已创建
- [ ] IMAP服务器可访问
- [ ] 邮箱密码正确
- [ ] 防火墙允许IMAP连接
- [ ] 服务器可正常启动
- [ ] API端点响应正常
- [ ] 测试脚本通过
- [ ] 验证码提取正常

完成以上配置后，您的RTBS扩展就可以使用真实的IMAP服务来自动获取Microsoft注册验证码了！
