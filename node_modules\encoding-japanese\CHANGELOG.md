# Change Log

## [2.0.0](https://github.com/polygonplanet/encoding.js/compare/1.0.30...2.0.0) (2022-03-29)

### Added

* Add `Encoding.version` ([bd3d6ef](https://github.com/polygonplanet/encoding.js/commit/bd3d6ef511a17c2d9671453e6c93618dae7ae9db))
* Add `fallback` option to `Encoding.convert` ([#23](https://github.com/polygonplanet/encoding.js/pull/23)) ([5622bfa](https://github.com/polygonplanet/encoding.js/commit/5622bfa4b2ee3981d664315b743094fcfd4d01a0))  Thanks [@tohutohu](https://github.com/tohutohu)

### Fixed

* Fix deprecated Buffer constructor ([b8fda07](https://github.com/polygonplanet/encoding.js/commit/b8fda07f6957f9197210fcda196cb2d6cc28e7a1))

### Removed

* Drop `bower` support ([981ea39](https://github.com/polygonplanet/encoding.js/commit/981ea3947021faa87e12774e0786c6b13fe09124))

## [1.0.30](https://github.com/polygonplanet/encoding.js/compare/1.0.29...1.0.30) (2019-09-12)

### Added

* Add LICENSE ([0224ebb](https://github.com/polygonplanet/encoding.js/commit/0224ebb620ae4058064f80ec3ec5898181595abe))

## [1.0.29](https://github.com/polygonplanet/encoding.js/compare/1.0.28...1.0.29) (2018-05-11)

### Fixed

* Fix can't find module in using 'require' ([#8](https://github.com/polygonplanet/encoding.js/issues/8)) ([5cf89b8](https://github.com/polygonplanet/encoding.js/commit/5cf89b85758d2466fd52a9690eed27ebaeba1e5e))

## [1.0.28](https://github.com/polygonplanet/encoding.js/compare/1.0.26...1.0.28) (2018-02-01)

### Changed

* Drop `Gruntfile.js` and modularize the code base by `browserify`

## [1.0.26](https://github.com/polygonplanet/encoding.js/compare/1.0.25...1.0.26) (2017-08-21)

### Fixed

* Fix the grammar in README.md ([#4](https://github.com/polygonplanet/encoding.js/pull/4)) Thanks [@iku000888](https://github.com/iku000888)

## [1.0.25](https://github.com/polygonplanet/encoding.js/compare/1.0.24...1.0.25) (2016-11-03)

### Fixed

* Fix argument decision of the detect method ([#3](https://github.com/polygonplanet/encoding.js/pull/3)) Thanks [@spring-raining](https://github.com/spring-raining)

## [1.0.24](https://github.com/polygonplanet/encoding.js/compare/1.0.23...1.0.24) (2015-09-22)

### Added

* Add `base64Encode` and `base64Decode` ([729bb4f](https://github.com/polygonplanet/encoding.js/commit/729bb4fac63dbfbea8dedefe874270ae5d6c2e21))

## [1.0.23](https://github.com/polygonplanet/encoding.js/compare/1.0.21...1.0.23) (2015-04-06)

### Fixed

* Fix internal `isObject()` method for old IE browsers ([#2](https://github.com/polygonplanet/encoding.js/pull/2)) ([32f6c02](https://github.com/polygonplanet/encoding.js/commit/32f6c02e290a36deb158357ddd1ebe34601cb4ea)) Thanks [@dmitrygorbenko](https://github.com/dmitrygorbenko)

## [1.0.21](https://github.com/polygonplanet/encoding.js/compare/1.0.20...1.0.21) (2015-02-12)

### Added

* Add bower.json ([d33bb7c](https://github.com/polygonplanet/encoding.js/commit/d33bb7c225e8e5c53100b6776a8c1d63b7a807e6))
