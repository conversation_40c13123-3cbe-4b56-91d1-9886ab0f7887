/**
 * 扩展上下文失效错误修复测试
 * 测试Extension context invalidated错误的处理
 */

console.log('🧪 开始测试扩展上下文失效错误修复...\n');

// 测试1: isExtensionContextValid函数
function testIsExtensionContextValid() {
  console.log('🔍 测试1: isExtensionContextValid函数');
  
  // 模拟函数（从content.js复制）
  function isExtensionContextValid() {
    try {
      // 尝试访问chrome.runtime，如果失效会抛出错误
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
        return true;
      }
      return false;
    } catch (error) {
      console.warn('🔄 扩展上下文检查失败:', error.message);
      return false;
    }
  }

  // 测试用例
  const testCases = [
    {
      description: '正常环境（chrome存在）',
      setup: () => {
        global.chrome = { runtime: { id: 'test-extension-id' } };
      },
      expected: true
    },
    {
      description: 'chrome未定义',
      setup: () => {
        global.chrome = undefined;
      },
      expected: false
    },
    {
      description: 'chrome.runtime未定义',
      setup: () => {
        global.chrome = {};
      },
      expected: false
    },
    {
      description: 'chrome.runtime.id未定义',
      setup: () => {
        global.chrome = { runtime: {} };
      },
      expected: false
    }
  ];

  let passed = 0;
  let failed = 0;

  testCases.forEach(testCase => {
    try {
      testCase.setup();
      const result = isExtensionContextValid();
      
      if (result === testCase.expected) {
        console.log(`✅ ${testCase.description}: ${result}`);
        passed++;
      } else {
        console.log(`❌ ${testCase.description}: 期望 ${testCase.expected}, 实际 ${result}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${testCase.description}: 抛出异常 ${error.message}`);
      failed++;
    }
  });

  console.log(`\n📊 测试结果: ${passed} 通过, ${failed} 失败`);
  return failed === 0;
}

// 测试2: safeChromeCall函数
function testSafeChromeCall() {
  console.log('\n🔍 测试2: safeChromeCall函数');
  
  // 模拟函数（从content.js复制）
  function isExtensionContextValid() {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  function safeChromeCall(apiCall, errorContext = '未知Chrome API调用') {
    if (!isExtensionContextValid()) {
      console.warn(`⚠️ 扩展上下文失效，跳过${errorContext}`);
      return false;
    }

    try {
      apiCall();
      return true;
    } catch (error) {
      if (error.message.includes('Extension context invalidated')) {
        console.warn(`⚠️ 扩展上下文失效，停止${errorContext}`);
      } else {
        console.error(`❌ ${errorContext}失败:`, error);
      }
      return false;
    }
  }

  // 测试用例
  const testCases = [
    {
      description: '扩展上下文失效时跳过API调用',
      setup: () => {
        global.chrome = undefined;
      },
      apiCall: () => {
        throw new Error('不应该执行到这里');
      },
      expected: false
    },
    {
      description: '正常API调用成功',
      setup: () => {
        global.chrome = { runtime: { id: 'test-extension-id' } };
      },
      apiCall: () => {
        // 模拟成功的API调用
        console.log('API调用成功');
      },
      expected: true
    },
    {
      description: '处理Extension context invalidated错误',
      setup: () => {
        global.chrome = { runtime: { id: 'test-extension-id' } };
      },
      apiCall: () => {
        throw new Error('Extension context invalidated.');
      },
      expected: false
    },
    {
      description: '处理其他类型的错误',
      setup: () => {
        global.chrome = { runtime: { id: 'test-extension-id' } };
      },
      apiCall: () => {
        throw new Error('其他类型的错误');
      },
      expected: false
    }
  ];

  let passed = 0;
  let failed = 0;

  testCases.forEach(testCase => {
    try {
      testCase.setup();
      const result = safeChromeCall(testCase.apiCall, '测试API调用');
      
      if (result === testCase.expected) {
        console.log(`✅ ${testCase.description}: ${result}`);
        passed++;
      } else {
        console.log(`❌ ${testCase.description}: 期望 ${testCase.expected}, 实际 ${result}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${testCase.description}: 抛出异常 ${error.message}`);
      failed++;
    }
  });

  console.log(`\n📊 测试结果: ${passed} 通过, ${failed} 失败`);
  return failed === 0;
}

// 测试3: 状态保存函数的扩展上下文处理
function testStateSaveWithContextCheck() {
  console.log('\n🔍 测试3: 状态保存函数的扩展上下文处理');
  
  // 模拟saveMicrosoftRegistrationState函数的核心逻辑
  function mockSaveMicrosoftRegistrationState() {
    // 检查扩展上下文是否有效
    if (!isExtensionContextValid()) {
      console.warn('⚠️ 扩展上下文失效，跳过状态保存');
      return false;
    }

    try {
      // 模拟chrome.storage.local.set调用
      if (typeof chrome === 'undefined' || !chrome.storage) {
        throw new Error('Extension context invalidated.');
      }
      
      console.log('状态保存成功');
      return true;
    } catch (error) {
      if (error.message.includes('Extension context invalidated')) {
        console.warn('⚠️ 扩展上下文失效，停止状态保存');
        return false;
      } else {
        console.error('❌ 保存状态失败:', error);
        return false;
      }
    }
  }

  function isExtensionContextValid() {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  // 测试用例
  const testCases = [
    {
      description: '扩展上下文有效时保存成功',
      setup: () => {
        global.chrome = { 
          runtime: { id: 'test-extension-id' },
          storage: { local: { set: () => {} } }
        };
      },
      expected: true
    },
    {
      description: '扩展上下文失效时跳过保存',
      setup: () => {
        global.chrome = undefined;
      },
      expected: false
    },
    {
      description: '处理Extension context invalidated异常',
      setup: () => {
        global.chrome = { 
          runtime: { id: 'test-extension-id' }
          // 故意不包含storage，模拟上下文失效
        };
      },
      expected: false
    }
  ];

  let passed = 0;
  let failed = 0;

  testCases.forEach(testCase => {
    try {
      testCase.setup();
      const result = mockSaveMicrosoftRegistrationState();
      
      if (result === testCase.expected) {
        console.log(`✅ ${testCase.description}: ${result}`);
        passed++;
      } else {
        console.log(`❌ ${testCase.description}: 期望 ${testCase.expected}, 实际 ${result}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${testCase.description}: 抛出异常 ${error.message}`);
      failed++;
    }
  });

  console.log(`\n📊 测试结果: ${passed} 通过, ${failed} 失败`);
  return failed === 0;
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行扩展上下文失效错误修复测试...\n');
  
  const results = [
    testIsExtensionContextValid(),
    testSafeChromeCall(),
    testStateSaveWithContextCheck()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('\n📋 总体测试结果:');
  if (allPassed) {
    console.log('✅ 所有测试通过！扩展上下文失效错误已修复。');
  } else {
    console.log('❌ 部分测试失败，需要进一步检查。');
  }
  
  return allPassed;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testIsExtensionContextValid,
    testSafeChromeCall,
    testStateSaveWithContextCheck,
    runAllTests
  };
  
  // 自动运行测试
  runAllTests();
} else {
  // 浏览器环境
  runAllTests();
}
