/**
 * 验证码问题排查工具
 * 用于诊断验证码无法获取的具体原因
 */

const http = require('http');

console.log('🔧 验证码问题排查工具启动...\n');

// HTTP请求工具函数
function httpRequest(method, url, data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: result });
        } catch (error) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 步骤1: 检查服务器状态
async function checkServerStatus() {
  console.log('🔍 步骤1: 检查IMAP服务器状态');
  
  try {
    const result = await httpRequest('GET', 'http://localhost:3000/health');
    console.log('✅ 服务器在线');
    console.log('📊 服务器状态:', result.data.status);
    console.log('📈 统计信息:', result.data.stats);
    
    if (result.data.imap) {
      console.log('📧 IMAP连接状态:', result.data.imap.connection);
      console.log('📧 最后检查时间:', result.data.imap.lastCheck);
    }
    
    return true;
  } catch (error) {
    console.log('❌ 服务器连接失败:', error.message);
    console.log('💡 请确保运行了 npm start 启动IMAP服务器');
    return false;
  }
}

// 步骤2: 检查配置信息
async function checkServerConfig() {
  console.log('\n🔍 步骤2: 检查服务器配置');
  
  try {
    const result = await httpRequest('GET', 'http://localhost:3000/config');
    console.log('✅ 配置获取成功');
    console.log('📧 IMAP服务器:', result.data.imap?.host);
    console.log('🔐 IMAP用户:', result.data.imap?.user);
    console.log('🔒 TLS启用:', result.data.imap?.tls);
    
    return true;
  } catch (error) {
    console.log('❌ 配置获取失败:', error.message);
    return false;
  }
}

// 步骤3: 列出所有可用的邮箱域名
async function listAvailableEmails() {
  console.log('\n🔍 步骤3: 检查可用的邮箱');
  
  const testDomains = ['s4464.cfd', 'example.com'];
  const testUsers = ['test', 'user1', 'admin'];
  
  for (const domain of testDomains) {
    console.log(`\n📧 检查域名: ${domain}`);
    
    for (const user of testUsers) {
      const email = `${user}@${domain}`;
      
      try {
        const result = await httpRequest('GET', `http://localhost:3000/emails/${encodeURIComponent(email)}`);
        
        if (result.data.success && result.data.emails && result.data.emails.length > 0) {
          console.log(`✅ ${email}: 找到 ${result.data.emails.length} 封邮件`);
          
          // 显示最近的邮件
          const recentEmails = result.data.emails.slice(0, 2);
          recentEmails.forEach((email, index) => {
            console.log(`   📨 邮件 ${index + 1}: ${email.subject} (${email.from})`);
          });
        } else {
          console.log(`❌ ${email}: 无邮件`);
        }
      } catch (error) {
        console.log(`❌ ${email}: 检查失败 - ${error.message}`);
      }
      
      // 避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
}

// 步骤4: 检查Microsoft相关邮件
async function checkMicrosoftEmails() {
  console.log('\n🔍 步骤4: 检查Microsoft验证邮件');
  
  const testEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  for (const email of testEmails) {
    console.log(`\n📧 检查邮箱: ${email}`);
    
    try {
      const result = await httpRequest('GET', `http://localhost:3000/emails/${encodeURIComponent(email)}`);
      
      if (result.data.success && result.data.emails) {
        const microsoftEmails = result.data.emails.filter(email => 
          email.from.toLowerCase().includes('microsoft') ||
          email.from.toLowerCase().includes('account-security-noreply') ||
          email.subject.toLowerCase().includes('security code') ||
          email.subject.toLowerCase().includes('verification') ||
          email.text?.includes('安全代码') ||
          email.text?.includes('验证码')
        );
        
        console.log(`📊 总邮件数: ${result.data.emails.length}`);
        console.log(`🔍 Microsoft相关邮件: ${microsoftEmails.length}`);
        
        if (microsoftEmails.length > 0) {
          console.log('✅ 找到Microsoft验证邮件:');
          microsoftEmails.slice(0, 3).forEach((email, index) => {
            console.log(`   📨 ${index + 1}. ${email.subject}`);
            console.log(`      发件人: ${email.from}`);
            console.log(`      时间: ${email.date}`);
            
            // 尝试提取验证码
            const codeMatch = email.text?.match(/(\d{6})/);
            if (codeMatch) {
              console.log(`      🔢 可能的验证码: ${codeMatch[1]}`);
            }
          });
        } else {
          console.log('❌ 未找到Microsoft验证邮件');
        }
      } else {
        console.log(`❌ 获取邮件失败: ${result.data.message || '未知错误'}`);
      }
    } catch (error) {
      console.log(`❌ 检查失败: ${error.message}`);
    }
    
    // 避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// 步骤5: 测试验证码提取
async function testVerificationCodeExtraction() {
  console.log('\n🔍 步骤5: 测试验证码提取');
  
  const testEmails = [
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  for (const email of testEmails) {
    console.log(`\n📧 测试邮箱: ${email}`);
    
    try {
      // 首先触发邮件检查
      console.log('📧 触发邮件检查...');
      const checkResult = await httpRequest('POST', `http://localhost:3000/check-email/${encodeURIComponent(email)}`);
      console.log('📧 邮件检查结果:', checkResult.data.success ? '✅ 成功' : '❌ 失败');
      
      // 等待3秒后尝试获取验证码
      console.log('⏳ 等待3秒后获取验证码...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const codeResult = await httpRequest('GET', `http://localhost:3000/verification-code/${encodeURIComponent(email)}`);
      
      if (codeResult.data.success && codeResult.data.code) {
        console.log(`✅ 验证码获取成功: ${codeResult.data.code}`);
        console.log(`📧 邮件时间: ${codeResult.data.emailTimestamp}`);
        console.log(`🎯 相关性评分: ${codeResult.data.relevanceScore}`);
      } else {
        console.log(`❌ 验证码获取失败: ${codeResult.data.message || '无消息'}`);
        
        // 显示详细错误信息
        if (codeResult.data.details) {
          console.log('📋 详细信息:', codeResult.data.details);
        }
      }
    } catch (error) {
      console.log(`❌ 测试失败: ${error.message}`);
    }
    
    // 避免请求过快
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

// 步骤6: 生成诊断报告
async function generateDiagnosticReport() {
  console.log('\n📋 生成诊断报告');
  
  try {
    const healthResult = await httpRequest('GET', 'http://localhost:3000/health');
    const statsResult = await httpRequest('GET', 'http://localhost:3000/stats');
    
    console.log('\n📊 诊断报告:');
    console.log('=====================================');
    console.log('服务器状态:', healthResult.data.status);
    console.log('总请求数:', statsResult.data.totalRequests);
    console.log('成功提取数:', statsResult.data.successfulExtractions);
    console.log('失败提取数:', statsResult.data.failedExtractions);
    console.log('缓存验证码数:', statsResult.data.cachedCodes);
    console.log('活跃请求数:', statsResult.data.activeRequests);
    console.log('=====================================');
    
    // 提供建议
    console.log('\n💡 建议:');
    if (statsResult.data.successfulExtractions === 0) {
      console.log('1. 检查邮箱中是否有Microsoft验证邮件');
      console.log('2. 确认邮箱域名配置正确');
      console.log('3. 检查IMAP服务器连接');
    }
    
    if (statsResult.data.failedExtractions > 0) {
      console.log('4. 检查邮件内容格式是否符合预期');
      console.log('5. 查看服务器日志获取更多错误信息');
    }
    
  } catch (error) {
    console.log('❌ 生成诊断报告失败:', error.message);
  }
}

// 主函数
async function main() {
  console.log('🚀 开始验证码问题排查...\n');
  
  const steps = [
    checkServerStatus,
    checkServerConfig,
    listAvailableEmails,
    checkMicrosoftEmails,
    testVerificationCodeExtraction,
    generateDiagnosticReport
  ];
  
  for (let i = 0; i < steps.length; i++) {
    try {
      await steps[i]();
    } catch (error) {
      console.log(`❌ 步骤 ${i + 1} 执行失败:`, error.message);
    }
    
    // 在步骤之间等待
    if (i < steps.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log('\n🎉 验证码问题排查完成！');
}

// 运行主函数
main().catch(console.error);
