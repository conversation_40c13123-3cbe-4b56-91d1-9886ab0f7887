/**
 * Popup状态显示测试脚本
 * 用于测试Microsoft注册状态在popup中的显示
 */

// 测试数据
const testStatuses = {
  idle: {
    currentAccount: null,
    currentStep: 'none',
    verificationStatus: '等待中',
    isProcessing: false,
    registrationComplete: false,
    systemVersion: '3.0.0',
    isV3System: true
  },
  
  processing: {
    currentAccount: '<EMAIL>',
    currentStep: 'signup',
    verificationStatus: '正在填写注册信息...',
    isProcessing: true,
    registrationComplete: false,
    systemVersion: '3.0.0',
    isV3System: true
  },
  
  verification: {
    currentAccount: '<EMAIL>',
    currentStep: 'verification_code',
    verificationStatus: '等待验证码邮件...',
    isProcessing: true,
    registrationComplete: false,
    systemVersion: '3.0.0',
    isV3System: true,
    v3Status: {
      controller: {
        isRunning: true,
        currentStage: 'verification_code',
        context: {
          account: '<EMAIL>',
          attempts: 1,
          imapService: {
            isWaitingForCode: true,
            startTime: Date.now() - 30000, // 30秒前开始
            retryCount: 2
          }
        }
      },
      systemStatus: 'running'
    }
  },
  
  verificationReceived: {
    currentAccount: '<EMAIL>',
    currentStep: 'verification_code',
    verificationStatus: '验证码已获取: 123456',
    isProcessing: true,
    registrationComplete: false,
    systemVersion: '3.0.0',
    isV3System: true,
    v3Status: {
      controller: {
        isRunning: true,
        currentStage: 'verification_code',
        context: {
          account: '<EMAIL>',
          attempts: 1,
          verificationCode: '123456'
        }
      },
      systemStatus: 'running'
    }
  },
  
  personalInfo: {
    currentAccount: '<EMAIL>',
    currentStep: 'personal_info',
    verificationStatus: '正在填写个人信息...',
    isProcessing: true,
    registrationComplete: false,
    systemVersion: '3.0.0',
    isV3System: true
  },
  
  completed: {
    currentAccount: '<EMAIL>',
    currentStep: 'completed',
    verificationStatus: '注册完成',
    isProcessing: false,
    registrationComplete: true,
    systemVersion: '3.0.0',
    isV3System: true
  },
  
  error: {
    currentAccount: '<EMAIL>',
    currentStep: 'verification_code',
    verificationStatus: '验证码获取失败 (已重试3次)',
    isProcessing: false,
    registrationComplete: false,
    systemVersion: '3.0.0',
    isV3System: true,
    v3Status: {
      controller: {
        isRunning: false,
        currentStage: 'verification_code',
        context: {
          account: '<EMAIL>',
          attempts: 3,
          errors: [
            { error: '网络连接失败', timestamp: Date.now() - 60000 },
            { error: 'IMAP服务超时', timestamp: Date.now() - 30000 },
            { error: '验证码获取失败', timestamp: Date.now() }
          ]
        }
      },
      systemStatus: 'error'
    }
  }
};

/**
 * 设置测试状态到Chrome存储
 */
async function setTestStatus(statusName) {
  if (!testStatuses[statusName]) {
    console.error('未知的测试状态:', statusName);
    return false;
  }
  
  try {
    await chrome.storage.local.set({
      'microsoft_registration_status': testStatuses[statusName]
    });
    
    console.log(`✅ 测试状态 "${statusName}" 已设置:`, testStatuses[statusName]);
    return true;
  } catch (error) {
    console.error('❌ 设置测试状态失败:', error);
    return false;
  }
}

/**
 * 清除测试状态
 */
async function clearTestStatus() {
  try {
    await chrome.storage.local.remove('microsoft_registration_status');
    console.log('✅ 测试状态已清除');
    return true;
  } catch (error) {
    console.error('❌ 清除测试状态失败:', error);
    return false;
  }
}

/**
 * 获取当前状态
 */
async function getCurrentStatus() {
  try {
    const result = await chrome.storage.local.get('microsoft_registration_status');
    console.log('📊 当前状态:', result.microsoft_registration_status);
    return result.microsoft_registration_status;
  } catch (error) {
    console.error('❌ 获取当前状态失败:', error);
    return null;
  }
}

/**
 * 运行所有测试状态
 */
async function runAllTests() {
  console.log('🚀 开始运行所有测试状态...');
  
  for (const [statusName, status] of Object.entries(testStatuses)) {
    console.log(`\n📋 测试状态: ${statusName}`);
    await setTestStatus(statusName);
    
    // 等待2秒让用户观察
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n✅ 所有测试状态运行完成');
}

/**
 * 模拟动态状态变化
 */
async function simulateDynamicStatus() {
  console.log('🎬 开始模拟动态状态变化...');
  
  // 1. 开始处理
  await setTestStatus('processing');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 2. 进入验证码阶段
  await setTestStatus('verification');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // 3. 收到验证码
  await setTestStatus('verificationReceived');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 4. 填写个人信息
  await setTestStatus('personalInfo');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 5. 完成注册
  await setTestStatus('completed');
  
  console.log('✅ 动态状态模拟完成');
}

/**
 * 测试错误状态
 */
async function testErrorStates() {
  console.log('🔥 开始测试错误状态...');
  
  // 设置错误状态
  await setTestStatus('error');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // 清除状态
  await clearTestStatus();
  
  console.log('✅ 错误状态测试完成');
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.testPopupStatus = {
    setTestStatus,
    clearTestStatus,
    getCurrentStatus,
    runAllTests,
    simulateDynamicStatus,
    testErrorStates,
    testStatuses
  };
  
  console.log('🎯 Popup状态测试工具已加载');
  console.log('使用方法:');
  console.log('  - testPopupStatus.setTestStatus("idle") - 设置空闲状态');
  console.log('  - testPopupStatus.setTestStatus("processing") - 设置处理中状态');
  console.log('  - testPopupStatus.setTestStatus("verification") - 设置验证码状态');
  console.log('  - testPopupStatus.setTestStatus("completed") - 设置完成状态');
  console.log('  - testPopupStatus.runAllTests() - 运行所有测试');
  console.log('  - testPopupStatus.simulateDynamicStatus() - 模拟动态变化');
  console.log('  - testPopupStatus.clearTestStatus() - 清除测试状态');
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    setTestStatus,
    clearTestStatus,
    getCurrentStatus,
    runAllTests,
    simulateDynamicStatus,
    testErrorStates,
    testStatuses
  };
}
