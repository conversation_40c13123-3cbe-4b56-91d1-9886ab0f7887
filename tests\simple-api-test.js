/**
 * 简单的API测试脚本
 * 使用Node.js内置模块测试IMAP服务器API
 */

const http = require('http');

console.log('🧪 开始简单API测试...\n');

// 简单的HTTP GET请求函数
function httpGet(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ status: res.statusCode, data: result });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
  });
}

// 简单的HTTP POST请求函数
function httpPost(url, postData = '') {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ status: res.statusCode, data: result });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
    
    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

// 测试1: 健康检查
async function testHealth() {
  console.log('🔍 测试1: 健康检查');
  
  try {
    const result = await httpGet('http://localhost:3000/health');
    console.log('✅ 健康检查成功');
    console.log('📊 状态码:', result.status);
    console.log('📋 响应:', JSON.stringify(result.data, null, 2));
    return true;
  } catch (error) {
    console.log('❌ 健康检查失败:', error.message);
    return false;
  }
}

// 测试2: 获取验证码
async function testGetVerificationCode() {
  console.log('\n🔍 测试2: 获取验证码');
  
  const testEmail = '<EMAIL>';
  
  try {
    const result = await httpGet(`http://localhost:3000/verification-code/${encodeURIComponent(testEmail)}`);
    console.log('📊 状态码:', result.status);
    console.log('📋 响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success && result.data.code) {
      console.log('✅ 验证码获取成功:', result.data.code);
      return true;
    } else {
      console.log('❌ 验证码获取失败:', result.data.message || '无消息');
      return false;
    }
  } catch (error) {
    console.log('❌ 验证码获取出错:', error.message);
    return false;
  }
}

// 测试3: 触发邮件检查
async function testTriggerEmailCheck() {
  console.log('\n🔍 测试3: 触发邮件检查');
  
  const testEmail = '<EMAIL>';
  
  try {
    const result = await httpPost(`http://localhost:3000/check-email/${encodeURIComponent(testEmail)}`);
    console.log('📊 状态码:', result.status);
    console.log('📋 响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log('✅ 邮件检查触发成功');
      return true;
    } else {
      console.log('❌ 邮件检查触发失败:', result.data.message || '无消息');
      return false;
    }
  } catch (error) {
    console.log('❌ 邮件检查触发出错:', error.message);
    return false;
  }
}

// 测试4: 获取邮件列表
async function testGetEmails() {
  console.log('\n🔍 测试4: 获取邮件列表');
  
  const testEmail = '<EMAIL>';
  
  try {
    const result = await httpGet(`http://localhost:3000/emails/${encodeURIComponent(testEmail)}`);
    console.log('📊 状态码:', result.status);
    console.log('📋 响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log(`✅ 邮件列表获取成功，找到 ${result.data.emails ? result.data.emails.length : 0} 封邮件`);
      return true;
    } else {
      console.log('❌ 邮件列表获取失败:', result.data.message || '无消息');
      return false;
    }
  } catch (error) {
    console.log('❌ 邮件列表获取出错:', error.message);
    return false;
  }
}

// 测试5: 获取统计信息
async function testGetStats() {
  console.log('\n🔍 测试5: 获取统计信息');
  
  try {
    const result = await httpGet('http://localhost:3000/stats');
    console.log('📊 状态码:', result.status);
    console.log('📋 响应:', JSON.stringify(result.data, null, 2));
    
    console.log('✅ 统计信息获取成功');
    return true;
  } catch (error) {
    console.log('❌ 统计信息获取出错:', error.message);
    return false;
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行简单API测试...\n');
  
  const tests = [
    testHealth,
    testTriggerEmailCheck,
    testGetVerificationCode,
    testGetEmails,
    testGetStats
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
    } catch (error) {
      console.log('❌ 测试执行出错:', error.message);
      results.push(false);
    }
    
    // 在测试之间等待一段时间
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  const passedCount = results.filter(r => r).length;
  const totalCount = results.length;
  
  console.log('\n📋 测试结果总结:');
  console.log(`✅ 通过: ${passedCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - passedCount}/${totalCount}`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有测试通过！IMAP服务器API正常工作。');
  } else {
    console.log('⚠️ 部分测试失败，需要进一步调试。');
  }
  
  return passedCount === totalCount;
}

// 运行测试
runAllTests().catch(console.error);
