/**
 * 验证码功能测试脚本
 * 测试修复后的验证码获取功能
 */

const http = require('http');

console.log('🧪 验证码功能测试开始...\n');

// HTTP请求工具函数
function httpRequest(method, url, data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: result });
        } catch (error) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 测试1: 检查服务器状态
async function testServerHealth() {
  console.log('🔍 测试1: 检查服务器状态');
  
  try {
    const result = await httpRequest('GET', 'http://localhost:3000/health');
    console.log('✅ 服务器状态:', result.data.status);
    console.log('📊 IMAP连接:', result.data.imap?.connection || '未知');
    return true;
  } catch (error) {
    console.log('❌ 服务器连接失败:', error.message);
    return false;
  }
}

// 测试2: 检查配置
async function testServerConfig() {
  console.log('\n🔍 测试2: 检查服务器配置');
  
  try {
    const result = await httpRequest('GET', 'http://localhost:3000/config');
    console.log('✅ IMAP服务器:', result.data.imap?.host);
    console.log('✅ IMAP用户:', result.data.imap?.user);
    console.log('✅ TLS启用:', result.data.imap?.tls);
    return true;
  } catch (error) {
    console.log('❌ 配置获取失败:', error.message);
    return false;
  }
}

// 测试3: 测试s4464.cfd邮箱的验证码获取
async function testS4464Email() {
  console.log('\n🔍 测试3: 测试s4464.cfd邮箱验证码获取');
  
  // 生成一个测试邮箱
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let testAccount = '';
  for (let i = 0; i < 12; i++) {
    testAccount += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  const testEmail = `${testAccount}@s4464.cfd`;
  
  console.log('📧 测试邮箱:', testEmail);
  
  try {
    // 首先触发邮件检查
    console.log('📧 触发邮件检查...');
    const checkResult = await httpRequest('POST', `http://localhost:3000/check-email/${encodeURIComponent(testEmail)}`);
    console.log('📧 邮件检查结果:', checkResult.data.success ? '✅ 成功' : '❌ 失败');
    
    // 等待3秒后获取验证码
    console.log('⏳ 等待3秒后获取验证码...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const codeResult = await httpRequest('GET', `http://localhost:3000/verification-code/${encodeURIComponent(testEmail)}`);
    
    if (codeResult.data.success && codeResult.data.code) {
      console.log(`✅ 验证码获取成功: ${codeResult.data.code}`);
      console.log(`📧 邮件时间: ${codeResult.data.emailTimestamp}`);
      return true;
    } else {
      console.log(`❌ 验证码获取失败: ${codeResult.data.message || '无消息'}`);
      
      // 显示详细错误信息
      if (codeResult.data.details) {
        console.log('📋 详细信息:', codeResult.data.details);
      }
      return false;
    }
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return false;
  }
}

// 测试4: 检查QQ邮箱中的邮件
async function testQQEmailContent() {
  console.log('\n🔍 测试4: 检查QQ邮箱中的邮件');
  
  const qqEmail = '<EMAIL>';
  
  try {
    const result = await httpRequest('GET', `http://localhost:3000/emails/${encodeURIComponent(qqEmail)}`);
    
    if (result.data.success && result.data.emails) {
      console.log(`📊 QQ邮箱总邮件数: ${result.data.emails.length}`);
      
      // 查找Microsoft相关邮件
      const microsoftEmails = result.data.emails.filter(email => 
        email.from.toLowerCase().includes('microsoft') ||
        email.from.toLowerCase().includes('account-security-noreply') ||
        email.subject.toLowerCase().includes('security code') ||
        email.subject.toLowerCase().includes('verification') ||
        email.text?.includes('安全代码') ||
        email.text?.includes('验证码')
      );
      
      console.log(`🔍 Microsoft相关邮件: ${microsoftEmails.length}`);
      
      if (microsoftEmails.length > 0) {
        console.log('✅ 找到Microsoft验证邮件:');
        microsoftEmails.slice(0, 3).forEach((email, index) => {
          console.log(`   📨 ${index + 1}. ${email.subject}`);
          console.log(`      发件人: ${email.from}`);
          console.log(`      时间: ${email.date}`);
          
          // 尝试提取验证码
          const codeMatch = email.text?.match(/(\d{6})/);
          if (codeMatch) {
            console.log(`      🔢 验证码: ${codeMatch[1]}`);
          }
        });
        return true;
      } else {
        console.log('❌ 未找到Microsoft验证邮件');
        
        // 显示最近的几封邮件
        if (result.data.emails.length > 0) {
          console.log('📋 最近的邮件:');
          result.data.emails.slice(0, 5).forEach((email, index) => {
            console.log(`   📨 ${index + 1}. ${email.subject} (${email.from})`);
          });
        }
        return false;
      }
    } else {
      console.log(`❌ 获取邮件失败: ${result.data.message || '未知错误'}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 检查失败: ${error.message}`);
    return false;
  }
}

// 测试5: 生成测试报告
async function generateTestReport() {
  console.log('\n📋 生成测试报告');
  
  try {
    const healthResult = await httpRequest('GET', 'http://localhost:3000/health');
    const statsResult = await httpRequest('GET', 'http://localhost:3000/stats');
    
    console.log('\n📊 测试报告:');
    console.log('=====================================');
    console.log('服务器状态:', healthResult.data.status);
    console.log('IMAP连接:', healthResult.data.imap?.connection || '未知');
    console.log('总请求数:', statsResult.data.totalRequests || 0);
    console.log('成功提取数:', statsResult.data.successfulExtractions || 0);
    console.log('失败提取数:', statsResult.data.failedExtractions || 0);
    console.log('=====================================');
    
    // 提供建议
    console.log('\n💡 建议:');
    if (statsResult.data.successfulExtractions === 0) {
      console.log('1. 确保在Microsoft注册过程中有验证邮件发送到邮箱');
      console.log('2. 检查邮箱转发配置是否正确');
      console.log('3. 验证IMAP服务器连接和权限');
    } else {
      console.log('✅ 验证码提取功能正常工作');
    }
    
  } catch (error) {
    console.log('❌ 生成测试报告失败:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始验证码功能测试...\n');
  
  const tests = [
    testServerHealth,
    testServerConfig,
    testS4464Email,
    testQQEmailContent,
    generateTestReport
  ];
  
  let passedTests = 0;
  
  for (let i = 0; i < tests.length; i++) {
    try {
      const result = await tests[i]();
      if (result) passedTests++;
    } catch (error) {
      console.log(`❌ 测试 ${i + 1} 执行失败:`, error.message);
    }
    
    // 在测试之间等待
    if (i < tests.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(`\n🎉 测试完成！通过: ${passedTests}/${tests.length - 1}`); // 减1因为最后一个是报告生成
}

// 运行测试
runTests().catch(console.error);
