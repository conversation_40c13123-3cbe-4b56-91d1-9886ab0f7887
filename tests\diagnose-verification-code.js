/**
 * 验证码问题诊断脚本
 */

// 诊断IMAP服务状态
async function diagnoseImapService() {
  console.log('🔍 === IMAP服务诊断 ===');
  
  if (typeof imapService === 'undefined') {
    console.error('❌ imapService对象不存在');
    return;
  }
  
  console.log('📊 IMAP服务状态:');
  console.log('  服务器URL:', imapService.serverUrl);
  console.log('  当前邮箱:', imapService.currentEmail);
  console.log('  是否等待验证码:', imapService.isWaitingForCode);
  console.log('  重试次数:', imapService.retryCount);
  console.log('  连续失败次数:', imapService.consecutiveFailures);
  console.log('  检查间隔:', imapService.checkIntervalTime);
  console.log('  开始时间:', imapService.startTime ? new Date(imapService.startTime).toISOString() : 'null');
  console.log('  最后检查时间:', imapService.lastCheckTime ? new Date(imapService.lastCheckTime).toISOString() : 'null');
  
  // 测试服务器连接
  console.log('\n🌐 测试服务器连接...');
  try {
    const response = await fetch(`${imapService.serverUrl}/health`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (response.ok) {
      const health = await response.json();
      console.log('✅ 服务器连接正常:', health);
    } else {
      console.error('❌ 服务器响应错误:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('❌ 服务器连接失败:', error.message);
    console.log('💡 请确保IMAP服务器正在运行在 http://localhost:3000');
  }
}

// 诊断状态逻辑
function diagnoseStateLogic() {
  console.log('\n🔍 === 状态逻辑诊断 ===');
  
  if (typeof msRegistrationState === 'undefined') {
    console.error('❌ msRegistrationState对象不存在');
    return;
  }
  
  console.log('📊 当前状态:');
  console.log('  当前步骤:', msRegistrationState.currentStep);
  console.log('  阶段索引:', msRegistrationState.currentStageIndex);
  console.log('  验证状态:', msRegistrationState.verificationStatus);
  console.log('  当前账号:', msRegistrationState.currentAccount);
  console.log('  是否处理中:', msRegistrationState.isProcessing);
  console.log('  已完成阶段:', msRegistrationState.stageCompleted);
  
  // 检查状态一致性
  console.log('\n🔍 状态一致性检查:');
  const currentStage = msRegistrationState.currentStep;
  const currentIndex = msRegistrationState.currentStageIndex;
  const expectedIndex = msRegistrationState.stageSequence.indexOf(currentStage);
  
  console.log('  当前阶段:', currentStage);
  console.log('  当前索引:', currentIndex);
  console.log('  期望索引:', expectedIndex);
  console.log('  索引一致性:', currentIndex === expectedIndex ? '✅ 一致' : '❌ 不一致');
  
  // 检查已完成阶段的逻辑性
  const completedStages = Object.keys(msRegistrationState.stageCompleted).filter(stage => msRegistrationState.stageCompleted[stage]);
  console.log('  已完成阶段:', completedStages);
  
  for (const stage of completedStages) {
    const stageIndex = msRegistrationState.stageSequence.indexOf(stage);
    if (stageIndex > currentIndex) {
      console.error(`  ❌ 逻辑错误: ${stage}(索引${stageIndex}) 已完成，但当前索引为 ${currentIndex}`);
    } else {
      console.log(`  ✅ ${stage}(索引${stageIndex}) 逻辑正确`);
    }
  }
}

// 诊断页面状态
function diagnosePage() {
  console.log('\n🔍 === 页面状态诊断 ===');
  
  console.log('📊 页面信息:');
  console.log('  URL:', window.location.href);
  console.log('  标题:', document.title);
  
  // 检查验证码输入框
  console.log('\n🔍 验证码输入框检查:');
  const codeInputs = [];
  for (let i = 0; i < 6; i++) {
    const input = document.querySelector(`#codeEntry-${i}`);
    if (input) {
      codeInputs.push(input);
      console.log(`  输入框 ${i}: 存在, 值="${input.value}"`);
    } else {
      console.log(`  输入框 ${i}: 不存在`);
    }
  }
  
  console.log(`  总计: ${codeInputs.length}/6 个输入框`);
  
  // 检查提交按钮
  console.log('\n🔍 提交按钮检查:');
  const submitButton = document.querySelector('button[type="submit"]');
  if (submitButton) {
    console.log('  提交按钮: 存在');
    console.log('  按钮文本:', submitButton.textContent);
    console.log('  是否禁用:', submitButton.disabled);
  } else {
    console.log('  提交按钮: 不存在');
  }
  
  // 检查页面类型
  console.log('\n🔍 页面类型检查:');
  if (typeof detectMicrosoftPageType !== 'undefined') {
    const pageType = detectMicrosoftPageType();
    console.log('  检测到的页面类型:', pageType);
  } else {
    console.log('  ❌ detectMicrosoftPageType函数不存在');
  }
}

// 手动触发验证码检查
async function manualCheckVerificationCode() {
  console.log('\n🔍 === 手动验证码检查 ===');
  
  if (!imapService.currentEmail) {
    console.error('❌ 没有设置当前邮箱');
    return;
  }
  
  console.log('📧 当前邮箱:', imapService.currentEmail);
  
  try {
    // 构建API URL
    const apiUrl = new URL(`${imapService.serverUrl}/verification-code/${encodeURIComponent(imapService.currentEmail)}`);
    apiUrl.searchParams.set('timestamp', Date.now().toString());
    
    console.log('📡 请求URL:', apiUrl.toString());
    
    const response = await fetch(apiUrl.toString(), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
    
    console.log('📡 响应状态:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('📧 API响应:', result);
      
      if (result.success && result.code) {
        console.log('🎉 找到验证码:', result.code);
        
        // 尝试填入验证码
        if (typeof fillVerificationCode !== 'undefined') {
          const fillResult = fillVerificationCode(result.code);
          console.log('填入结果:', fillResult ? '✅ 成功' : '❌ 失败');
        } else {
          console.log('⚠️ fillVerificationCode函数不存在，手动填入:');
          simulateCodeInput(result.code);
        }
      } else {
        console.log('⚠️ 没有找到验证码');
      }
    } else {
      console.error('❌ API请求失败:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('❌ 验证码检查失败:', error);
  }
}

// 模拟验证码输入
function simulateCodeInput(code) {
  console.log('🎯 模拟验证码输入:', code);
  
  if (code.length !== 6) {
    console.error('❌ 验证码长度不正确:', code.length);
    return false;
  }
  
  for (let i = 0; i < 6; i++) {
    const input = document.querySelector(`#codeEntry-${i}`);
    if (input) {
      input.value = code[i];
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      console.log(`  输入框 ${i}: ${code[i]}`);
    } else {
      console.error(`❌ 输入框 ${i} 不存在`);
      return false;
    }
  }
  
  console.log('✅ 验证码输入完成');
  
  // 尝试提交
  setTimeout(() => {
    const submitButton = document.querySelector('button[type="submit"]');
    if (submitButton && !submitButton.disabled) {
      console.log('🎯 自动提交验证码');
      submitButton.click();
    } else {
      console.log('⚠️ 提交按钮不可用，请手动提交');
    }
  }, 500);
  
  return true;
}

// 修复状态逻辑
function fixStateLogic() {
  console.log('\n🔧 === 修复状态逻辑 ===');
  
  if (typeof msRegistrationState === 'undefined') {
    console.error('❌ msRegistrationState对象不存在');
    return;
  }
  
  // 检查并修复状态不一致问题
  const completedStages = Object.keys(msRegistrationState.stageCompleted).filter(stage => msRegistrationState.stageCompleted[stage]);
  const currentIndex = msRegistrationState.currentStageIndex;
  
  let needsFix = false;
  let maxCompletedIndex = -1;
  
  for (const stage of completedStages) {
    const stageIndex = msRegistrationState.stageSequence.indexOf(stage);
    if (stageIndex > currentIndex) {
      needsFix = true;
      maxCompletedIndex = Math.max(maxCompletedIndex, stageIndex);
    }
  }
  
  if (needsFix) {
    console.log('🔧 检测到状态不一致，开始修复...');
    
    const nextStageIndex = maxCompletedIndex + 1;
    if (nextStageIndex < msRegistrationState.stageSequence.length) {
      const nextStage = msRegistrationState.stageSequence[nextStageIndex];
      console.log(`🔧 推进到下一阶段: ${nextStage}(索引${nextStageIndex})`);
      
      if (typeof setCurrentStage !== 'undefined') {
        setCurrentStage(nextStage);
        console.log('✅ 状态修复完成');
      } else {
        // 手动修复
        msRegistrationState.currentStep = nextStage;
        msRegistrationState.currentStageIndex = nextStageIndex;
        msRegistrationState.verificationStatus = nextStage === 'verification_code' ? '等待验证码' : `处理${nextStage}阶段`;
        console.log('✅ 手动状态修复完成');
      }
    } else {
      console.log('🔧 所有阶段已完成，设置为完成状态');
      msRegistrationState.currentStep = 'completed';
      msRegistrationState.currentStageIndex = msRegistrationState.stageSequence.length;
      msRegistrationState.verificationStatus = '注册完成';
    }
  } else {
    console.log('✅ 状态逻辑正常，无需修复');
  }
}

// 运行完整诊断
async function runFullDiagnosis() {
  console.log('🚀 === 开始完整诊断 ===');
  
  await diagnoseImapService();
  diagnoseStateLogic();
  diagnosePage();
  
  console.log('\n🔧 === 自动修复 ===');
  fixStateLogic();
  
  console.log('\n🏁 === 诊断完成 ===');
  console.log('💡 如果IMAP服务器连接失败，请确保服务器正在运行');
  console.log('💡 如果状态逻辑有问题，已尝试自动修复');
  console.log('💡 可以使用 verifyDiag.manual() 手动检查验证码');
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.verifyDiag = {
    imap: diagnoseImapService,
    state: diagnoseStateLogic,
    page: diagnosePage,
    manual: manualCheckVerificationCode,
    simulate: simulateCodeInput,
    fix: fixStateLogic,
    full: runFullDiagnosis
  };
  
  console.log('🔍 验证码诊断工具已加载');
  console.log('使用方法:');
  console.log('  verifyDiag.full() - 运行完整诊断');
  console.log('  verifyDiag.imap() - 诊断IMAP服务');
  console.log('  verifyDiag.state() - 诊断状态逻辑');
  console.log('  verifyDiag.page() - 诊断页面状态');
  console.log('  verifyDiag.manual() - 手动检查验证码');
  console.log('  verifyDiag.fix() - 修复状态逻辑');
  console.log('  verifyDiag.simulate("123456") - 模拟输入验证码');
}
