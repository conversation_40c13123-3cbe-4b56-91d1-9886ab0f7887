/**
 * 测试脚本：验证状态一致性修复
 */

// 测试状态一致性
function testStateConsistency() {
  console.log('🧪 开始测试状态一致性...');
  
  // 测试各个阶段的状态设置
  const testStages = [
    'data_permission',
    'signup', 
    'personal_info',
    'name',
    'email_verification',
    'verification_code',
    'captcha',
    'login_complete',
    'rewards_welcome'
  ];
  
  testStages.forEach(stage => {
    console.log(`\n📋 测试阶段: ${stage}`);
    
    // 设置阶段
    if (typeof setCurrentStage !== 'undefined') {
      const result = setCurrentStage(stage);
      console.log(`  设置结果: ${result ? '✅ 成功' : '❌ 失败'}`);
      
      // 检查状态
      if (typeof msRegistrationState !== 'undefined') {
        console.log(`  当前步骤: ${msRegistrationState.currentStep}`);
        console.log(`  阶段索引: ${msRegistrationState.currentStageIndex}`);
        console.log(`  验证状态: ${msRegistrationState.verificationStatus}`);
        
        // 验证一致性
        const expectedIndex = msRegistrationState.stageSequence.indexOf(stage);
        const isConsistent = msRegistrationState.currentStageIndex === expectedIndex;
        console.log(`  索引一致性: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`);
        
        const hasVerificationStatus = !!msRegistrationState.verificationStatus;
        console.log(`  验证状态存在: ${hasVerificationStatus ? '✅ 是' : '❌ 否'}`);
      }
    } else {
      console.log('  ❌ setCurrentStage函数不存在');
    }
  });
  
  console.log('\n🏁 状态一致性测试完成');
}

// 测试状态恢复
function testStateRecovery() {
  console.log('🧪 开始测试状态恢复...');
  
  // 模拟保存状态
  const testState = {
    currentAccount: '<EMAIL>',
    currentStep: 'verification_code',
    currentStageIndex: 4,
    stageCompleted: {
      'data_permission': true,
      'signup': true,
      'personal_info': true,
      'name': true
    },
    attempts: 1,
    verificationStatus: '等待验证码',
    registrationComplete: false,
    timestamp: new Date().toISOString()
  };
  
  // 保存到存储
  chrome.storage.local.set({
    'microsoft_registration_status': testState
  }, () => {
    console.log('✅ 测试状态已保存到存储');
    
    // 清除内存状态
    if (typeof msRegistrationState !== 'undefined') {
      msRegistrationState.currentAccount = null;
      msRegistrationState.currentStep = 'none';
      msRegistrationState.currentStageIndex = -1;
      msRegistrationState.verificationStatus = '等待中';
      console.log('🧹 内存状态已清除');
    }
    
    // 测试恢复
    setTimeout(() => {
      if (typeof loadMicrosoftRegistrationStateFromStorage !== 'undefined') {
        loadMicrosoftRegistrationStateFromStorage();
        console.log('🔄 状态恢复函数已调用');
        
        // 等待恢复完成后检查
        setTimeout(() => {
          if (typeof msRegistrationState !== 'undefined') {
            console.log('📊 恢复后的状态:');
            console.log(`  账号: ${msRegistrationState.currentAccount}`);
            console.log(`  步骤: ${msRegistrationState.currentStep}`);
            console.log(`  索引: ${msRegistrationState.currentStageIndex}`);
            console.log(`  验证状态: ${msRegistrationState.verificationStatus}`);
            
            const isRecovered = msRegistrationState.currentAccount === testState.currentAccount &&
                              msRegistrationState.currentStep === testState.currentStep &&
                              msRegistrationState.currentStageIndex === testState.currentStageIndex;
            
            console.log(`恢复结果: ${isRecovered ? '✅ 成功' : '❌ 失败'}`);
          }
        }, 1000);
      } else {
        console.log('❌ loadMicrosoftRegistrationStateFromStorage函数不存在');
      }
    }, 500);
  });
}

// 测试验证码处理
function testVerificationCodeHandling() {
  console.log('🧪 开始测试验证码处理...');
  
  // 设置测试账号
  if (typeof msRegistrationState !== 'undefined') {
    msRegistrationState.currentAccount = '<EMAIL>';
    msRegistrationState.currentStep = 'verification_code';
    msRegistrationState.currentStageIndex = 4;
    msRegistrationState.verificationStatus = '等待验证码';
    
    if (typeof imapService !== 'undefined') {
      imapService.currentEmail = '<EMAIL>';
    }
    
    console.log('✅ 测试账号状态已设置');
  }
  
  // 测试验证码处理函数
  if (typeof handleVerificationCodePage !== 'undefined') {
    console.log('🎯 调用验证码处理函数...');
    handleVerificationCodePage();
    console.log('✅ 验证码处理函数已调用');
  } else {
    console.log('❌ handleVerificationCodePage函数不存在');
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行所有状态测试...');
  
  testStateConsistency();
  
  setTimeout(() => {
    testStateRecovery();
  }, 2000);
  
  setTimeout(() => {
    testVerificationCodeHandling();
  }, 4000);
  
  console.log('📋 所有测试已启动，请查看控制台输出');
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.stateTest = {
    consistency: testStateConsistency,
    recovery: testStateRecovery,
    verification: testVerificationCodeHandling,
    all: runAllTests
  };
  
  console.log('🧪 状态测试工具已加载');
  console.log('使用方法:');
  console.log('  stateTest.consistency() - 测试状态一致性');
  console.log('  stateTest.recovery() - 测试状态恢复');
  console.log('  stateTest.verification() - 测试验证码处理');
  console.log('  stateTest.all() - 运行所有测试');
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testStateConsistency,
    testStateRecovery,
    testVerificationCodeHandling,
    runAllTests
  };
}
