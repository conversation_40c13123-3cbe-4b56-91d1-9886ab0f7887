# 微软注册阶段化处理指南

## 🎯 问题解决

### 原问题
- 弹出面板显示的阶段与实际阶段不符
- 在人机验证阶段仍显示"个人信息"
- 全阶段检测浪费资源，缺乏阶段顺序控制

### 解决方案
重新设计为基于状态机的阶段化处理系统，确保：
1. **严格的阶段顺序** - 只有当前阶段或下一个预期阶段才会被处理
2. **准确的状态显示** - 弹出面板显示真实的当前阶段
3. **资源优化** - 避免不必要的全阶段检测

## 📋 完整阶段流程

### 阶段序列
```javascript
stageSequence: [
  'data_permission',    // 1. 数据许可页面
  'signup',            // 2. 邮箱填写页面  
  'email_verification', // 3. 等待邮件页面
  'verification_code',  // 4. 验证码输入页面
  'personal_info',     // 5. 个人信息页面
  'name',              // 6. 姓名信息页面
  'captcha',           // 7. 人机验证页面
  'login_complete',    // 8. 登录完成页面
  'rewards_welcome'    // 9. 奖励欢迎页面
]
```

### 阶段转换规则
```javascript
allowedTransitions: {
  'none': ['data_permission', 'signup'],
  'data_permission': ['signup'],
  'signup': ['email_verification'],
  'email_verification': ['verification_code'],
  'verification_code': ['personal_info'],
  'personal_info': ['name'],
  'name': ['captcha'],
  'captcha': ['login_complete'],
  'login_complete': ['rewards_welcome'],
  'rewards_welcome': ['completed']
}
```

## 🔧 核心功能

### 1. 阶段管理函数

#### `getCurrentStage()`
- 返回当前正在处理的阶段

#### `getExpectedStage()`
- 返回下一个预期的阶段

#### `shouldProcessStage(detectedStage)`
- 判断是否应该处理检测到的阶段
- 只有以下情况才返回 true：
  - 检测到的阶段是期望的下一个阶段
  - 检测到的阶段是当前阶段且未完成
  - 是有效的阶段转换

#### `advanceToNextStage(completedStage)`
- 标记当前阶段为已完成
- 推进到下一个阶段
- 更新状态并保存

### 2. 智能处理逻辑

```javascript
function handleMicrosoftRegistration() {
  // 1. 检测当前页面状态
  const currentState = detectCurrentPageState();
  
  // 2. 检查是否应该处理这个阶段
  if (!shouldProcessStage(currentState.type)) {
    console.log(`⏭️ 跳过阶段 ${currentState.type}`);
    return;
  }
  
  // 3. 执行阶段特定的处理
  // 4. 完成后推进到下一阶段
}
```

## 📊 阶段状态显示

### 弹出面板更新
```javascript
// popup.js 中的阶段名称映射
const stepNames = {
  'none': '未开始',
  'data_permission': '数据许可',
  'signup': '邮箱填写',
  'email_verification': '等待邮件',
  'verification_code': '验证码',
  'personal_info': '个人信息',
  'name': '姓名信息',
  'captcha': '人机验证',
  'login_complete': '登录完成',
  'rewards_welcome': '欢迎页面',
  'completed': '已完成'
};
```

### 状态保存增强
```javascript
// 保存更详细的状态信息
microsoft_registration_status: {
  currentAccount: '账号信息',
  currentStep: '当前阶段',
  currentStageIndex: '阶段索引',
  stageCompleted: '已完成的阶段',
  attempts: '尝试次数',
  verificationStatus: '验证状态',
  registrationComplete: '是否完成',
  timestamp: '时间戳'
}
```

## 🎯 预期效果

### 1. 准确的阶段显示
- ✅ 弹出面板显示真实的当前阶段
- ✅ 在人机验证阶段显示"人机验证"而不是"个人信息"

### 2. 资源优化
- ✅ 只处理当前阶段或下一个预期阶段
- ✅ 避免不必要的全阶段检测
- ✅ 减少CPU和内存使用

### 3. 更好的错误处理
- ✅ 严格的阶段转换验证
- ✅ 详细的调试日志
- ✅ 阶段完成状态跟踪

## 🔍 调试信息

### 控制台日志示例
```
🎯 完成阶段: personal_info
➡️ 进入下一阶段: name
🔍 阶段检查: 当前=name, 期望=name, 检测到=name
✅ 检测到期望的阶段: name
🎯 开始处理阶段: name
```

### 状态跟踪
- 每个阶段的完成状态
- 当前阶段索引
- 允许的下一步转换
- 处理状态和错误信息

## 🧪 测试验证

### 1. 阶段跳过测试
- 在人机验证阶段，不应该处理个人信息页面的检测
- 只有检测到人机验证相关的页面才会处理

### 2. 状态显示测试
- 弹出面板应该显示正确的当前阶段
- 阶段名称应该与实际处理阶段一致

### 3. 转换验证测试
- 只允许有效的阶段转换
- 跳过无效的阶段检测

## 📝 注意事项

1. **阶段顺序严格** - 必须按照定义的序列进行
2. **状态同步** - 确保内存状态与存储状态一致
3. **错误恢复** - 处理异常情况下的阶段重置
4. **向后兼容** - 处理旧版本的状态数据

## 🔄 升级说明

### 主要变更
1. **状态管理** - 从简单状态到状态机
2. **处理逻辑** - 从全检测到阶段化检测
3. **显示更新** - 更准确的阶段名称映射

### 兼容性
- 自动处理旧版本的状态数据
- 平滑升级，无需手动干预
