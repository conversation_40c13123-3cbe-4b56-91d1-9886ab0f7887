<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTBS 当前状态调试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-section {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #007acc;
            background-color: #f8f9fa;
        }
        .status-title {
            font-weight: bold;
            color: #007acc;
            margin-bottom: 10px;
        }
        .status-item {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .status-label {
            font-weight: bold;
            display: inline-block;
            width: 150px;
        }
        .status-value {
            color: #333;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #856404;
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a9e;
        }
        .refresh-btn {
            background-color: #28a745;
        }
        .refresh-btn:hover {
            background-color: #1e7e34;
        }
        .test-btn {
            background-color: #ffc107;
            color: #212529;
        }
        .test-btn:hover {
            background-color: #e0a800;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 RTBS 当前状态调试</h1>
        
        <div class="status-section">
            <div class="status-title">🎛️ 控制面板</div>
            <button onclick="refreshStatus()" class="refresh-btn">🔄 刷新状态</button>
            <button onclick="testImapServer()" class="test-btn">🧪 测试IMAP服务器</button>
            <button onclick="testCurrentEmail()" class="test-btn">📧 测试当前邮箱</button>
            <button onclick="clearDebugInfo()">🧹 清除调试信息</button>
        </div>

        <div class="status-section">
            <div class="status-title">📊 Microsoft注册状态</div>
            <div id="registration-status">正在加载...</div>
        </div>

        <div class="status-section">
            <div class="status-title">📧 IMAP服务状态</div>
            <div id="imap-status">正在加载...</div>
        </div>

        <div class="status-section">
            <div class="status-title">🌐 服务器连接状态</div>
            <div id="server-status">正在加载...</div>
        </div>

        <div class="status-section">
            <div class="status-title">🔧 调试信息</div>
            <div id="debug-info">点击上方按钮开始调试...</div>
        </div>
    </div>

    <script>
        // 获取Microsoft注册状态
        async function getRegistrationStatus() {
            try {
                // 尝试从Chrome存储获取状态
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    return new Promise((resolve) => {
                        chrome.storage.local.get(['microsoft_registration_status'], (result) => {
                            resolve(result.microsoft_registration_status || null);
                        });
                    });
                } else {
                    return null;
                }
            } catch (error) {
                console.error('获取注册状态失败:', error);
                return null;
            }
        }

        // 测试IMAP服务器连接
        async function testImapServer() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = '<div class="warning">🔍 正在测试IMAP服务器连接...</div>';

            try {
                const response = await fetch('http://localhost:3000/health');
                const result = await response.json();
                
                debugDiv.innerHTML = `
                    <div class="success">✅ IMAP服务器连接成功</div>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                debugDiv.innerHTML = `
                    <div class="error">❌ IMAP服务器连接失败: ${error.message}</div>
                    <div class="warning">请确保运行了 npm start 启动IMAP服务器</div>
                `;
            }
        }

        // 测试当前邮箱的验证码获取
        async function testCurrentEmail() {
            const debugDiv = document.getElementById('debug-info');
            
            // 首先获取当前邮箱
            const status = await getRegistrationStatus();
            if (!status || !status.currentAccount) {
                debugDiv.innerHTML = '<div class="error">❌ 没有找到当前邮箱账号</div>';
                return;
            }

            const email = status.currentAccount;
            debugDiv.innerHTML = `<div class="warning">🔍 正在测试邮箱: ${email}</div>`;

            try {
                // 首先触发邮件检查
                const checkResponse = await fetch(`http://localhost:3000/check-email/${encodeURIComponent(email)}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const checkResult = await checkResponse.json();

                // 等待3秒后获取验证码
                setTimeout(async () => {
                    try {
                        const codeResponse = await fetch(`http://localhost:3000/verification-code/${encodeURIComponent(email)}`);
                        const codeResult = await codeResponse.json();

                        debugDiv.innerHTML = `
                            <div class="status-item">
                                <span class="status-label">测试邮箱:</span>
                                <span class="status-value">${email}</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">邮件检查结果:</span>
                                <span class="status-value">${checkResult.success ? '✅ 成功' : '❌ 失败'}</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">验证码获取结果:</span>
                                <span class="status-value">${codeResult.success ? `✅ 成功: ${codeResult.code}` : `❌ 失败: ${codeResult.message}`}</span>
                            </div>
                            <pre>邮件检查详情: ${JSON.stringify(checkResult, null, 2)}</pre>
                            <pre>验证码详情: ${JSON.stringify(codeResult, null, 2)}</pre>
                        `;
                    } catch (error) {
                        debugDiv.innerHTML += `<div class="error">❌ 验证码获取出错: ${error.message}</div>`;
                    }
                }, 3000);

            } catch (error) {
                debugDiv.innerHTML = `<div class="error">❌ 邮件检查出错: ${error.message}</div>`;
            }
        }

        // 刷新所有状态
        async function refreshStatus() {
            // 刷新注册状态
            const registrationDiv = document.getElementById('registration-status');
            const status = await getRegistrationStatus();
            
            if (status) {
                registrationDiv.innerHTML = `
                    <div class="status-item">
                        <span class="status-label">当前账号:</span>
                        <span class="status-value">${status.currentAccount || '无'}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">当前步骤:</span>
                        <span class="status-value">${status.currentStep || '无'}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">验证状态:</span>
                        <span class="status-value">${status.verificationStatus || '无'}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">是否处理中:</span>
                        <span class="status-value">${status.isProcessing ? '是' : '否'}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">注册完成:</span>
                        <span class="status-value">${status.registrationComplete ? '是' : '否'}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">尝试次数:</span>
                        <span class="status-value">${status.attempts || 0}</span>
                    </div>
                `;
            } else {
                registrationDiv.innerHTML = '<div class="error">❌ 无法获取注册状态</div>';
            }

            // 刷新IMAP状态
            const imapDiv = document.getElementById('imap-status');
            imapDiv.innerHTML = '正在检查IMAP状态...';
            
            // 这里可以添加更多IMAP状态检查
            imapDiv.innerHTML = `
                <div class="status-item">
                    <span class="status-label">服务器地址:</span>
                    <span class="status-value">http://localhost:3000</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前邮箱:</span>
                    <span class="status-value">${status?.currentAccount || '无'}</span>
                </div>
            `;

            // 刷新服务器状态
            const serverDiv = document.getElementById('server-status');
            try {
                const response = await fetch('http://localhost:3000/health');
                const result = await response.json();
                
                serverDiv.innerHTML = `
                    <div class="success">✅ 服务器在线</div>
                    <div class="status-item">
                        <span class="status-label">状态:</span>
                        <span class="status-value">${result.status}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">启动时间:</span>
                        <span class="status-value">${result.server?.startTime || '未知'}</span>
                    </div>
                `;
            } catch (error) {
                serverDiv.innerHTML = `
                    <div class="error">❌ 服务器离线</div>
                    <div class="warning">请运行 npm start 启动IMAP服务器</div>
                `;
            }
        }

        // 清除调试信息
        function clearDebugInfo() {
            document.getElementById('debug-info').innerHTML = '调试信息已清除';
        }

        // 页面加载时自动刷新状态
        window.addEventListener('load', () => {
            refreshStatus();
        });
    </script>
</body>
</html>
