<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microsoft注册状态显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Microsoft注册状态显示测试</h1>
        <p>此页面用于测试Microsoft注册系统的状态显示功能</p>

        <div class="test-section">
            <h3>系统检查</h3>
            <button class="button" onclick="checkSystem()">检查系统状态</button>
            <button class="button" onclick="checkExtension()">检查扩展状态</button>
            <div id="systemStatus" class="status-info">等待检查...</div>
        </div>

        <div class="test-section">
            <h3>状态获取测试</h3>
            <button class="button" onclick="getRegistrationStatus()">获取注册状态</button>
            <button class="button" onclick="getStorageStatus()">获取存储状态</button>
            <div id="statusResult" class="status-info">等待获取...</div>
        </div>

        <div class="test-section">
            <h3>模拟状态测试</h3>
            <button class="button" onclick="simulateStatus('idle')">模拟空闲状态</button>
            <button class="button" onclick="simulateStatus('processing')">模拟处理中状态</button>
            <button class="button" onclick="simulateStatus('verification')">模拟验证码状态</button>
            <button class="button" onclick="simulateStatus('completed')">模拟完成状态</button>
            <div id="simulateResult" class="status-info">等待模拟...</div>
        </div>

        <div class="test-section">
            <h3>调试日志</h3>
            <button class="button" onclick="clearLog()">清除日志</button>
            <div id="debugLog" class="log">日志将显示在这里...</div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('debugLog');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '日志已清除...<br>';
        }

        async function checkSystem() {
            log('开始检查系统状态...', 'info');
            
            try {
                // 检查是否在Microsoft页面
                const isMicrosoftPage = location.href.includes('microsoft.com') || 
                                       location.href.includes('live.com') || 
                                       location.href.includes('outlook.com');
                
                log(`当前页面: ${location.href}`, 'info');
                log(`是否Microsoft页面: ${isMicrosoftPage}`, isMicrosoftPage ? 'success' : 'warning');
                
                // 检查扩展是否可用
                const hasChrome = typeof chrome !== 'undefined';
                const hasRuntime = hasChrome && chrome.runtime;
                
                log(`Chrome API可用: ${hasChrome}`, hasChrome ? 'success' : 'error');
                log(`Runtime API可用: ${hasRuntime}`, hasRuntime ? 'success' : 'error');
                
                document.getElementById('systemStatus').innerHTML = `
                    <strong>系统状态:</strong><br>
                    当前页面: ${location.href}<br>
                    Microsoft页面: ${isMicrosoftPage ? '是' : '否'}<br>
                    Chrome API: ${hasChrome ? '可用' : '不可用'}<br>
                    Runtime API: ${hasRuntime ? '可用' : '不可用'}
                `;
                
            } catch (error) {
                log(`系统检查失败: ${error.message}`, 'error');
                document.getElementById('systemStatus').innerHTML = `<span class="error">检查失败: ${error.message}</span>`;
            }
        }

        async function checkExtension() {
            log('开始检查扩展状态...', 'info');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome扩展API不可用');
                }
                
                // 尝试发送消息到content script
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                if (tabs.length === 0) {
                    throw new Error('无法获取当前标签页');
                }
                
                log(`当前标签页ID: ${tabs[0].id}`, 'info');
                
                const response = await chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'getMicrosoftRegistrationStatus'
                });
                
                log(`扩展响应: ${JSON.stringify(response)}`, response.success ? 'success' : 'error');
                
            } catch (error) {
                log(`扩展检查失败: ${error.message}`, 'error');
            }
        }

        async function getRegistrationStatus() {
            log('开始获取注册状态...', 'info');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.tabs) {
                    throw new Error('Chrome扩展API不可用');
                }
                
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                const response = await chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'getMicrosoftRegistrationStatus'
                });
                
                log(`状态获取响应: ${JSON.stringify(response, null, 2)}`, 'info');
                
                if (response && response.success) {
                    const status = response.status;
                    document.getElementById('statusResult').innerHTML = `
                        <strong>注册状态:</strong><br>
                        当前账号: ${status.currentAccount || '未生成'}<br>
                        当前步骤: ${status.currentStep || '无'}<br>
                        验证状态: ${status.verificationStatus || '无'}<br>
                        是否处理中: ${status.isProcessing ? '是' : '否'}<br>
                        是否完成: ${status.registrationComplete ? '是' : '否'}<br>
                        系统版本: ${status.systemVersion || '未知'}<br>
                        V3系统: ${status.isV3System ? '是' : '否'}
                    `;
                } else {
                    document.getElementById('statusResult').innerHTML = `<span class="error">获取状态失败</span>`;
                }
                
            } catch (error) {
                log(`获取状态失败: ${error.message}`, 'error');
                document.getElementById('statusResult').innerHTML = `<span class="error">获取失败: ${error.message}</span>`;
            }
        }

        async function getStorageStatus() {
            log('开始获取存储状态...', 'info');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.storage) {
                    throw new Error('Chrome存储API不可用');
                }
                
                const result = await chrome.storage.local.get('microsoft_registration_status');
                log(`存储状态: ${JSON.stringify(result, null, 2)}`, 'info');
                
                document.getElementById('statusResult').innerHTML = `
                    <strong>存储状态:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
            } catch (error) {
                log(`获取存储状态失败: ${error.message}`, 'error');
            }
        }

        async function simulateStatus(type) {
            log(`开始模拟${type}状态...`, 'info');
            
            const mockStatuses = {
                idle: {
                    currentAccount: null,
                    currentStep: 'none',
                    verificationStatus: '等待中',
                    isProcessing: false,
                    registrationComplete: false
                },
                processing: {
                    currentAccount: '<EMAIL>',
                    currentStep: 'signup',
                    verificationStatus: '处理中...',
                    isProcessing: true,
                    registrationComplete: false
                },
                verification: {
                    currentAccount: '<EMAIL>',
                    currentStep: 'verification_code',
                    verificationStatus: '等待验证码...',
                    isProcessing: true,
                    registrationComplete: false
                },
                completed: {
                    currentAccount: '<EMAIL>',
                    currentStep: 'completed',
                    verificationStatus: '注册完成',
                    isProcessing: false,
                    registrationComplete: true
                }
            };
            
            const mockStatus = mockStatuses[type];
            if (mockStatus) {
                try {
                    await chrome.storage.local.set({
                        'microsoft_registration_status': mockStatus
                    });
                    
                    log(`${type}状态模拟成功`, 'success');
                    document.getElementById('simulateResult').innerHTML = `
                        <strong>模拟状态 (${type}):</strong><br>
                        <pre>${JSON.stringify(mockStatus, null, 2)}</pre>
                    `;
                } catch (error) {
                    log(`模拟状态失败: ${error.message}`, 'error');
                }
            }
        }

        // 页面加载时自动检查系统
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动检查...', 'info');
            checkSystem();
        });
    </script>
</body>
</html>
