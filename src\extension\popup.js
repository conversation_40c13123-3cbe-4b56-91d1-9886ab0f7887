// RewardTracker 弹窗脚本
document.addEventListener('DOMContentLoaded', async function() {
  const elements = {
    statusIndicator: document.getElementById('statusIndicator'),
    currentAccount: document.getElementById('currentAccount'),
    searchProgress: document.getElementById('searchProgress'),
    daysProgress: document.getElementById('daysProgress'),
    refreshBtn: document.getElementById('refreshBtn'),
    viewLogsBtn: document.getElementById('viewLogsBtn'),
    clearLogsBtn: document.getElementById('clearLogsBtn'),
    openTargetBtn: document.getElementById('openTargetBtn'),
    logsContainer: document.getElementById('logsContainer'),
    logsList: document.getElementById('logsList'),
    biliStatusIndicator: document.getElementById('biliStatusIndicator'),
    biliPageStatus: document.getElementById('biliPageStatus'),
    biliSearchStatus: document.getElementById('biliSearchStatus'),
    biliSearchTerm: document.getElementById('biliSearchTerm'),
    biliCompleteStatus: document.getElementById('biliCompleteStatus'),
    // Microsoft注册面板元素
    msStatusIndicator: document.getElementById('msStatusIndicator'),
    msCurrentAccount: document.getElementById('msCurrentAccount'),
    msCurrentStep: document.getElementById('msCurrentStep'),
    msVerifyStatus: document.getElementById('msVerifyStatus'),
    msRegistrationStatus: document.getElementById('msRegistrationStatus'),
    // 面板容器元素
    rewardTrackerPanel: document.getElementById('rewardTrackerPanel'),
    biliSearchPanel: document.getElementById('biliSearchPanel'),
    microsoftRegPanel: document.getElementById('microsoftRegPanel'),
    emptyState: document.getElementById('emptyState')
  };

  // 加载统计数据
  async function loadStats() {
    try {
      const result = await chrome.storage.local.get(['rewardTracker_lastData']);
      
      if (result.rewardTracker_lastData) {
        const data = result.rewardTracker_lastData;
        
        // 显示账号信息
        if (data.userAccount) {
          elements.currentAccount.textContent = data.userAccount;
          elements.currentAccount.className = 'account-value';
        } else {
          elements.currentAccount.textContent = '暂无数据';
          elements.currentAccount.className = 'account-value waiting';
        }

        // 显示天数信息
        if (data.searchDaysInfo) {
          elements.daysProgress.textContent = data.searchDaysInfo.progress || '--/--';
          const progress = data.searchDaysInfo.progress || '--/--';
          elements.searchProgress.textContent = `连续搜索${progress}天`;
        } else {
          elements.daysProgress.textContent = '--/--';
          elements.searchProgress.textContent = '连续搜索--/--天';
        }
      } else {
        // 没有数据时的默认显示
        elements.currentAccount.textContent = '暂无数据';
        elements.currentAccount.className = 'account-value waiting';
        elements.daysProgress.textContent = '--/--';
        elements.searchProgress.textContent = '暂无数据';
      }
    } catch (error) {
      // 错误时的默认显示
      elements.currentAccount.textContent = '暂无数据';
      elements.currentAccount.className = 'account-value waiting';
      elements.daysProgress.textContent = '--/--';
      elements.searchProgress.textContent = '暂无数据';
    }
  }

  // 检查当前标签页状态
  async function checkCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // 检查是否在必应奖励页面
      const isOnRewardsPage = tab && tab.url && (
        tab.url.includes('rewards.bing.com') ||
        tab.url.includes('bing.com/rewards') ||
        tab.url.includes('www.bing.com/rewards')
      );

      // 检查是否在哔哩搜索页面
      const isOnBiliSearchPage = tab && tab.url && (
        tab.url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW') ||
        tab.url.includes('cn.bing.com/search?q=哔哩哔哩') ||
        tab.url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9')
      );

      // 检查是否在Microsoft注册相关页面
      const isOnMicrosoftRegPage = tab && tab.url && (
        tab.url.includes('signup.live.com') ||
        tab.url.includes('login.live.com') ||
        (tab.url.includes('rewards.bing.com') && tab.url.includes('form=BILREW'))
      );

      // 面板显示控制逻辑
      if (isOnRewardsPage) {
        // 在必应奖励页面：只显示RewardTracker面板
        elements.rewardTrackerPanel.style.display = 'block';
        elements.biliSearchPanel.style.display = 'none';
        elements.microsoftRegPanel.style.display = 'none';
        elements.emptyState.style.display = 'none';
        elements.biliSearchPanel.classList.remove('with-margin');
        elements.microsoftRegPanel.classList.remove('with-margin');
        elements.statusIndicator.className = 'indicator active';
        await loadStats();
      } else if (isOnBiliSearchPage) {
        // 在哔哩搜索页面：只显示BiliSearch面板
        elements.rewardTrackerPanel.style.display = 'none';
        elements.biliSearchPanel.style.display = 'block';
        elements.microsoftRegPanel.style.display = 'none';
        elements.emptyState.style.display = 'none';
        elements.biliSearchPanel.classList.remove('with-margin');
        elements.microsoftRegPanel.classList.remove('with-margin');
        elements.biliStatusIndicator.className = 'indicator active';
        await loadBiliStatus();
      } else if (isOnMicrosoftRegPage) {
        // 在Microsoft注册页面：只显示Microsoft注册面板
        elements.rewardTrackerPanel.style.display = 'none';
        elements.biliSearchPanel.style.display = 'none';
        elements.microsoftRegPanel.style.display = 'block';
        elements.emptyState.style.display = 'none';
        elements.biliSearchPanel.classList.remove('with-margin');
        elements.microsoftRegPanel.classList.remove('with-margin');
        elements.msStatusIndicator.className = 'indicator active';
        await loadMicrosoftStatus();
      } else {
        // 在其他页面：不显示任何面板，显示空状态
        elements.rewardTrackerPanel.style.display = 'none';
        elements.biliSearchPanel.style.display = 'none';
        elements.microsoftRegPanel.style.display = 'none';
        elements.emptyState.style.display = 'block';
        elements.biliSearchPanel.classList.remove('with-margin');
        elements.microsoftRegPanel.classList.remove('with-margin');
      }
    } catch (error) {
      // 错误时显示空状态
      elements.rewardTrackerPanel.style.display = 'none';
      elements.biliSearchPanel.style.display = 'none';
      elements.emptyState.style.display = 'block';
      elements.biliSearchPanel.classList.remove('with-margin');
    }
  }

  // 加载哔哩搜索状态
  async function loadBiliStatus() {
    try {
      const result = await chrome.storage.local.get('biliSearch_status');

      if (result.biliSearch_status) {
        const status = result.biliSearch_status;

        // 页面加载状态
        if (status.pageLoaded) {
          elements.biliPageStatus.textContent = '已完成';
          elements.biliPageStatus.className = 'account-value';
        } else {
          elements.biliPageStatus.textContent = '加载中...';
          elements.biliPageStatus.className = 'account-value waiting';
        }

        // 搜索修改状态
        if (status.searchModified) {
          elements.biliSearchStatus.textContent = '已修改';
          elements.biliSearchStatus.className = 'days-progress';
        } else if (status.pageLoaded) {
          elements.biliSearchStatus.textContent = '修改中...';
          elements.biliSearchStatus.className = 'days-progress';
        } else {
          elements.biliSearchStatus.textContent = '等待中...';
          elements.biliSearchStatus.className = 'days-progress';
        }

        // 搜索词显示
        if (status.searchTerm) {
          elements.biliSearchTerm.textContent = status.searchTerm;
          elements.biliSearchTerm.className = 'details-content';
        } else {
          elements.biliSearchTerm.textContent = '--';
          elements.biliSearchTerm.className = 'details-content';
        }

        // 搜索完成状态
        if (status.searchCompleted) {
          elements.biliCompleteStatus.textContent = '已完成';
          elements.biliCompleteStatus.className = 'details-content';
        } else if (status.searchModified) {
          elements.biliCompleteStatus.textContent = '搜索中...';
          elements.biliCompleteStatus.className = 'details-content';
        } else {
          elements.biliCompleteStatus.textContent = '等待中...';
          elements.biliCompleteStatus.className = 'details-content';
        }
      } else {
        // 没有状态数据时的默认显示
        elements.biliPageStatus.textContent = '等待中...';
        elements.biliPageStatus.className = 'account-value waiting';
        elements.biliSearchStatus.textContent = '等待中...';
        elements.biliSearchStatus.className = 'days-progress';
        elements.biliSearchTerm.textContent = '--';
        elements.biliSearchTerm.className = 'details-content';
        elements.biliCompleteStatus.textContent = '等待中...';
        elements.biliCompleteStatus.className = 'details-content';
      }
    } catch (error) {
      // 错误时的默认显示
      elements.biliPageStatus.textContent = '错误';
      elements.biliPageStatus.className = 'account-value waiting';
      elements.biliSearchStatus.textContent = '错误';
      elements.biliSearchStatus.className = 'days-progress';
      elements.biliSearchTerm.textContent = '错误';
      elements.biliSearchTerm.className = 'details-content';
      elements.biliCompleteStatus.textContent = '错误';
      elements.biliCompleteStatus.className = 'details-content';
    }
  }

  // 加载Microsoft注册状态 - v3版本
  async function loadMicrosoftStatus() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!tab || !tab.id) {
        console.log('⚠️ 无法获取当前标签页');
        displayErrorMicrosoftStatus('无法获取当前标签页');
        return;
      }

      // 首先尝试从content script获取v3系统状态
      let status = null;
      let statusSource = 'none';

      try {
        console.log('📡 正在从content script获取状态...');
        const response = await chrome.tabs.sendMessage(tab.id, {
          action: 'getMicrosoftRegistrationStatus'
        });

        if (response && response.success) {
          status = response.status;
          statusSource = 'content_script';
          console.log('📊 获取到v3系统状态:', status);
        } else {
          console.log('⚠️ content script响应无效:', response);
        }
      } catch (error) {
        console.log('⚠️ 无法获取v3系统状态，尝试本地存储:', error.message);
      }

      // 如果没有v3状态，回退到本地存储
      if (!status) {
        try {
          const result = await chrome.storage.local.get('microsoft_registration_status');
          status = result.microsoft_registration_status;
          statusSource = 'local_storage';
          console.log('📊 使用本地存储状态:', status);
        } catch (error) {
          console.error('❌ 获取本地存储状态失败:', error);
        }
      }

      if (status) {
        // 显示系统版本信息
        displaySystemVersion(status, statusSource);

        // 显示状态信息
        displayMicrosoftStatus(status);

        console.log(`✅ Microsoft状态加载成功 (来源: ${statusSource})`);

      } else {
        // 没有状态数据时的默认显示
        console.log('⚠️ 没有找到Microsoft注册状态数据');
        displayNoDataMicrosoftStatus();
      }
    } catch (error) {
      console.error('❌ 加载Microsoft注册状态失败:', error);
      displayErrorMicrosoftStatus(error.message);
    }
  }

  /**
   * 显示系统版本信息
   */
  function displaySystemVersion(status, source) {
    const versionInfo = status.systemVersion || status.version || '未知';
    const isV3 = status.isV3System || status.v3Status;

    // 在控制台显示详细的版本和来源信息
    console.log(`📋 系统版本: ${versionInfo}, V3系统: ${isV3}, 数据来源: ${source}`);

    // 在状态指示器旁边显示版本信息
    if (elements.msStatusIndicator) {
      const versionBadge = document.createElement('span');
      versionBadge.className = `version-badge ${isV3 ? 'v3' : 'legacy'}`;
      versionBadge.textContent = isV3 ? 'v3.0' : 'v2.x';
      versionBadge.title = `系统版本: ${versionInfo}`;

      // 移除旧的版本标识
      const oldBadge = elements.msStatusIndicator.parentNode.querySelector('.version-badge');
      if (oldBadge) {
        oldBadge.remove();
      }

      elements.msStatusIndicator.parentNode.appendChild(versionBadge);
    }
  }

  /**
   * 显示Microsoft注册状态
   */
  function displayMicrosoftStatus(status) {
    // 确保status对象存在
    if (!status) {
      console.log('⚠️ displayMicrosoftStatus: 状态对象为空');
      displayDefaultMicrosoftStatus();
      return;
    }

    // 调试信息：显示完整的状态对象
    console.log('📊 displayMicrosoftStatus: 状态对象详情:', {
      currentAccount: status.currentAccount,
      currentStep: status.currentStep,
      verificationStatus: status.verificationStatus,
      isProcessing: status.isProcessing,
      registrationComplete: status.registrationComplete,
      isV3System: status.isV3System,
      hasV3Status: !!status.v3Status
    });

    // 当前账号显示
    if (status.currentAccount) {
      elements.msCurrentAccount.textContent = status.currentAccount;
      elements.msCurrentAccount.className = 'account-value';
      console.log('✅ 显示账号:', status.currentAccount);
    } else {
      elements.msCurrentAccount.textContent = '未生成';
      elements.msCurrentAccount.className = 'account-value waiting';
      console.log('⚠️ 账号未生成');
    }

    // 当前步骤显示 - 添加安全检查
    const currentStep = status.currentStep || 'none';
    const stepName = getStepDisplayName(currentStep) || '未开始';
    elements.msCurrentStep.textContent = stepName;
    elements.msCurrentStep.className = 'days-progress';

    // 如果是v3系统，显示更详细的状态
    if (status.v3Status && status.v3Status.controller) {
      const controller = status.v3Status.controller;

      // 显示系统状态
      const systemStatus = getSystemStatusDisplay(status.v3Status.systemStatus);
      elements.msCurrentStep.textContent = `${stepName} (${systemStatus})`;

      // 显示错误信息（如果有）
      if (controller.context && controller.context.errors && controller.context.errors.length > 0) {
        const lastError = controller.context.errors[controller.context.errors.length - 1];
        elements.msVerifyStatus.textContent = `错误: ${lastError.error}`;
        elements.msVerifyStatus.className = 'details-content error';
      } else {
        // 验证状态显示 - 增强版本
        let verificationText = status.verificationStatus || '等待中';

        // 如果是验证码阶段，提供更详细的状态信息
        if (status.currentStep === 'verification_code') {
          if (controller.context) {
            const context = controller.context;

            // 检查IMAP服务状态
            if (context.imapService) {
              if (context.imapService.isWaitingForCode) {
                const elapsedTime = context.imapService.startTime ?
                  Math.floor((Date.now() - context.imapService.startTime) / 1000) : 0;
                verificationText = `正在获取验证码... (${elapsedTime}秒)`;
              } else if (context.imapService.retryCount > 0) {
                verificationText = `重试获取验证码 (${context.imapService.retryCount}次)`;
              }
            }

            // 检查是否有验证码
            if (context.verificationCode) {
              verificationText = `验证码已获取: ${context.verificationCode}`;
            }

            // 检查邮箱状态
            if (context.account) {
              verificationText += ` - 邮箱: ${context.account}`;
            }
          }
        }

        elements.msVerifyStatus.textContent = verificationText;
        elements.msVerifyStatus.className = 'details-content';
      }
    } else {
      // 兼容模式显示 - 增强版本
      let verificationText = status.verificationStatus || '等待中';

      // 如果是验证码阶段，提供更详细的状态信息
      if (status.currentStep === 'verification_code') {
        // 检查当前账号
        if (status.currentAccount) {
          verificationText += ` - 邮箱: ${status.currentAccount}`;
        }

        // 检查处理状态
        if (status.isProcessing) {
          verificationText = '正在处理验证码...';
        }

        // 如果状态为空或默认值，提供更有用的信息
        if (!status.verificationStatus || status.verificationStatus === '等待中') {
          verificationText = '等待验证码邮件...';
          if (status.currentAccount) {
            verificationText += ` (${status.currentAccount})`;
          }
        }
      }

      elements.msVerifyStatus.textContent = verificationText;
      elements.msVerifyStatus.className = 'details-content';
    }

    // 注册状态显示
    if (status.registrationComplete) {
      elements.msRegistrationStatus.textContent = '已完成';
      elements.msRegistrationStatus.className = 'details-content success';
    } else if (status.isProcessing) {
      elements.msRegistrationStatus.textContent = '进行中';
      elements.msRegistrationStatus.className = 'details-content processing';
    } else {
      elements.msRegistrationStatus.textContent = '等待中';
      elements.msRegistrationStatus.className = 'details-content';
    }
  }

  /**
   * 显示默认状态
   */
  function displayDefaultMicrosoftStatus() {
    elements.msCurrentAccount.textContent = '等待中...';
    elements.msCurrentAccount.className = 'account-value waiting';
    elements.msCurrentStep.textContent = '等待中...';
    elements.msCurrentStep.className = 'days-progress';
    elements.msVerifyStatus.textContent = '等待中...';
    elements.msVerifyStatus.className = 'details-content';
    elements.msRegistrationStatus.textContent = '等待中...';
    elements.msRegistrationStatus.className = 'details-content';
  }

  /**
   * 显示无数据状态
   */
  function displayNoDataMicrosoftStatus() {
    elements.msCurrentAccount.textContent = '--/--';
    elements.msCurrentAccount.className = 'account-value waiting';
    elements.msCurrentStep.textContent = '--/--';
    elements.msCurrentStep.className = 'days-progress';
    elements.msVerifyStatus.textContent = '--/--';
    elements.msVerifyStatus.className = 'details-content';
    elements.msRegistrationStatus.textContent = '--/--';
    elements.msRegistrationStatus.className = 'details-content';
  }

  /**
   * 显示错误状态
   */
  function displayErrorMicrosoftStatus(error) {
    elements.msCurrentAccount.textContent = '获取失败';
    elements.msCurrentAccount.className = 'account-value waiting';
    elements.msCurrentStep.textContent = '获取失败';
    elements.msCurrentStep.className = 'days-progress';
    elements.msVerifyStatus.textContent = error || '获取失败';
    elements.msVerifyStatus.className = 'details-content error';
    elements.msRegistrationStatus.textContent = '获取失败';
    elements.msRegistrationStatus.className = 'details-content error';
  }

  /**
   * 获取系统状态显示文本
   */
  function getSystemStatusDisplay(systemStatus) {
    const statusMap = {
      'idle': '空闲',
      'ready': '就绪',
      'running': '运行中',
      'error': '错误',
      'completed': '完成'
    };

    return statusMap[systemStatus] || systemStatus;
  }

  // 获取步骤显示名称
  function getStepDisplayName(step) {
    // 添加安全检查
    if (!step || typeof step !== 'string') {
      return '未开始';
    }

    const stepNames = {
      'none': '未开始',
      'data_permission': '数据许可',
      'signup': '邮箱填写',
      'email_verification': '等待邮件',
      'verification_code': '验证码',
      'personal_info': '个人信息',
      'name': '姓名信息',
      'captcha': '人机验证',
      'login_complete': '登录完成',
      'rewards_welcome': '欢迎页面',
      'completed': '已完成'
    };
    return stepNames[step] || step;
  }

  // 显示无数据消息（非目标页面时）
  function showNoDataMessage() {
    elements.currentAccount.textContent = '--/--';
    elements.currentAccount.className = 'account-value waiting';
    elements.daysProgress.textContent = '--/--';
    elements.searchProgress.textContent = '--/--';
  }

  // 显示哔哩搜索无数据消息（非哔哩搜索页面时）
  function showBiliNoDataMessage() {
    elements.biliPageStatus.textContent = '--/--';
    elements.biliPageStatus.className = 'account-value waiting';
    elements.biliSearchStatus.textContent = '--/--';
    elements.biliSearchStatus.className = 'days-progress';
    elements.biliSearchTerm.textContent = '--/--';
    elements.biliSearchTerm.className = 'details-content';
    elements.biliCompleteStatus.textContent = '--/--';
    elements.biliCompleteStatus.className = 'details-content';
  }

  // 加载日志数据
  async function loadLogs() {
    try {
      const result = await chrome.storage.local.get(null);
      const logs = [];

      // 筛选出日志条目
      for (const [key, value] of Object.entries(result)) {
        if (key.startsWith('log_') && value.timestamp) {
          logs.push(value);
        }
      }

      // 按时间排序，最新的在前
      logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      if (logs.length === 0) {
        elements.logsList.innerHTML = '<div style="text-align: center; color: #666; font-size: 11px; padding: 10px;">暂无日志记录</div>';
      } else {
        const logsHtml = logs.slice(0, 10).map(log => {
          const date = new Date(log.timestamp).toLocaleString('zh-CN');
          return `
            <div class="log-item">
              <div class="log-time">${date}</div>
              <div>账号: ${log.userAccount || '未知'}</div>
              <div>文件: ${log.filename || '未知'}</div>
            </div>
          `;
        }).join('');
        elements.logsList.innerHTML = logsHtml;
      }
    } catch (error) {
      elements.logsList.innerHTML = '<div style="text-align: center; color: #f44336; font-size: 11px; padding: 10px;">加载日志失败</div>';
    }
  }

  // 刷新数据
  elements.refreshBtn.addEventListener('click', async function() {
    await checkCurrentTab();
  });

  // 显示/隐藏日志
  let logsVisible = false;
  elements.viewLogsBtn.addEventListener('click', async function() {
    if (!logsVisible) {
      await loadLogs(); // 加载日志数据
      elements.logsContainer.style.display = 'block';
      elements.viewLogsBtn.textContent = '隐藏';
      logsVisible = true;
    } else {
      elements.logsContainer.style.display = 'none';
      elements.viewLogsBtn.textContent = '查看';
      logsVisible = false;
    }
  });

  // 清除日志
  elements.clearLogsBtn.addEventListener('click', async function() {
    try {
      await chrome.storage.local.clear();
      await checkCurrentTab();
    } catch (error) {
      // 清除失败时静默处理
    }
  });

  // 打开目标页面
  elements.openTargetBtn.addEventListener('click', function() {
    chrome.tabs.create({ url: 'https://rewards.bing.com/?form=BILREW' });
  });

  // 自动刷新Microsoft注册状态
  let autoRefreshInterval = null;

  function startAutoRefresh() {
    // 清除现有的定时器
    if (autoRefreshInterval) {
      clearInterval(autoRefreshInterval);
    }

    // 每3秒自动刷新一次状态
    autoRefreshInterval = setInterval(async () => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab && tab.url && (
          tab.url.includes('signup.live.com') ||
          tab.url.includes('account.live.com') ||
          tab.url.includes('login.live.com') ||
          tab.url.includes('account.microsoft.com')
        )) {
          // 只在Microsoft相关页面时刷新状态
          await loadMicrosoftStatus();
        }
      } catch (error) {
        // 静默处理错误，避免控制台噪音
      }
    }, 3000);
  }

  function stopAutoRefresh() {
    if (autoRefreshInterval) {
      clearInterval(autoRefreshInterval);
      autoRefreshInterval = null;
    }
  }

  // 页面可见性变化时控制自动刷新
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      stopAutoRefresh();
    } else {
      startAutoRefresh();
    }
  });

  // 初始化
  await checkCurrentTab();

  // 启动自动刷新
  startAutoRefresh();

  // 页面卸载时清理定时器
  window.addEventListener('beforeunload', () => {
    stopAutoRefresh();
  });
});