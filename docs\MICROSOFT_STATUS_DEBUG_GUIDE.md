# Microsoft注册状态显示调试指南

## 问题描述
Microsoft注册面板一直没有显示任何信息，显示为默认的 `--/--` 状态。

## 可能的原因

### 1. V3系统初始化问题
- V3系统可能没有正确初始化
- 依赖的脚本文件加载顺序问题
- 函数导出问题

### 2. 状态同步问题
- Content script和popup之间的消息传递失败
- 状态同步定时器没有正常工作
- 扩展上下文失效

### 3. 页面检测问题
- 页面URL检测逻辑可能有问题
- 面板显示逻辑可能有bug

## 调试步骤

### 步骤1: 检查扩展加载状态

1. 打开Chrome开发者工具 (F12)
2. 切换到Console标签
3. 查看是否有以下日志：
   ```
   ✅ Microsoft注册系统 v3.0 已加载
   🔍 检查v3系统依赖:
   ```

### 步骤2: 检查V3系统初始化

在控制台中运行：
```javascript
// 检查V3系统是否存在
console.log('MicrosoftRegistrationSystemV3:', typeof window.MicrosoftRegistrationSystemV3);
console.log('getMicrosoftRegistrationSystemV3:', typeof window.getMicrosoftRegistrationSystemV3);

// 尝试手动初始化
if (typeof window.getMicrosoftRegistrationSystemV3 === 'function') {
  const system = window.getMicrosoftRegistrationSystemV3();
  console.log('V3系统实例:', system);
  console.log('是否已初始化:', system?.isInitialized);
}
```

### 步骤3: 检查状态获取

在控制台中运行：
```javascript
// 检查content script是否响应
chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
  chrome.tabs.sendMessage(tabs[0].id, {
    action: 'getMicrosoftRegistrationStatus'
  }, function(response) {
    console.log('状态响应:', response);
  });
});
```

### 步骤4: 检查本地存储

在控制台中运行：
```javascript
chrome.storage.local.get('microsoft_registration_status', function(result) {
  console.log('本地存储状态:', result);
});
```

### 步骤5: 使用测试工具

1. 打开测试页面：`tests/test-microsoft-status-display.html`
2. 点击各种测试按钮检查系统状态
3. 使用测试脚本模拟不同状态

## 测试工具使用

### 1. 状态显示测试页面
打开 `tests/test-microsoft-status-display.html` 进行系统检查。

### 2. Popup状态测试脚本
在控制台中加载测试脚本：
```javascript
// 加载测试脚本（需要先将test-popup-status.js内容复制到控制台）

// 设置不同的测试状态
testPopupStatus.setTestStatus('processing');  // 处理中状态
testPopupStatus.setTestStatus('verification'); // 验证码状态
testPopupStatus.setTestStatus('completed');   // 完成状态

// 运行动态测试
testPopupStatus.simulateDynamicStatus();

// 清除测试状态
testPopupStatus.clearTestStatus();
```

## 修复措施

### 1. 添加了自动刷新机制
- Popup现在每3秒自动刷新一次状态
- 只在Microsoft相关页面时刷新
- 页面隐藏时停止刷新以节省资源

### 2. 改进了错误处理
- 添加了更详细的调试日志
- 改进了状态显示函数的错误处理
- 添加了不同的状态显示（无数据、错误、默认）

### 3. 增强了状态同步
- 改进了V3系统状态同步逻辑
- 添加了状态变化检测
- 增强了调试信息输出

## 常见问题解决

### 问题1: 面板显示 `--/--`
**原因**: 没有获取到状态数据
**解决**: 
1. 检查是否在Microsoft页面
2. 检查扩展是否正确加载
3. 使用测试脚本设置模拟状态

### 问题2: 面板显示 `等待中...`
**原因**: 获取到了默认状态但没有实际数据
**解决**:
1. 检查V3系统是否初始化
2. 检查状态同步是否正常工作
3. 手动触发状态更新

### 问题3: 面板显示 `获取失败`
**原因**: 状态获取过程中发生错误
**解决**:
1. 查看控制台错误日志
2. 检查扩展权限
3. 重新加载扩展

## 实时调试

### 启用详细日志
在控制台中运行：
```javascript
// 启用详细调试日志
localStorage.setItem('debug_microsoft_registration', 'true');

// 重新加载页面以应用设置
location.reload();
```

### 监控状态变化
```javascript
// 监控存储变化
chrome.storage.onChanged.addListener(function(changes, namespace) {
  if (changes.microsoft_registration_status) {
    console.log('状态变化:', changes.microsoft_registration_status);
  }
});
```

## 联系支持

如果以上步骤都无法解决问题，请提供以下信息：
1. 控制台完整日志
2. 当前页面URL
3. 扩展版本信息
4. 测试步骤和结果

## 更新日志

### v3.0.1 (当前版本)
- 添加了自动刷新机制
- 改进了错误处理和调试信息
- 增强了状态同步逻辑
- 添加了测试工具和调试指南
