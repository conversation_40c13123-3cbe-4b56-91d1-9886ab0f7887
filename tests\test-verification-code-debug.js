/**
 * 验证码获取调试测试
 * 用于诊断验证码无法获取的问题
 */

console.log('🧪 开始验证码获取调试测试...\n');

// 测试1: 检查IMAP服务器连接
async function testImapServerConnection() {
  console.log('🔍 测试1: 检查IMAP服务器连接');
  
  try {
    const response = await fetch('http://localhost:3000/health');
    const result = await response.json();
    
    console.log('✅ IMAP服务器连接成功');
    console.log('📊 服务器状态:', result.status);
    console.log('📈 统计信息:', result.stats);
    console.log('⚙️ 配置信息:', result.config);
    
    return true;
  } catch (error) {
    console.log('❌ IMAP服务器连接失败:', error.message);
    return false;
  }
}

// 测试2: 测试验证码获取API
async function testVerificationCodeAPI() {
  console.log('\n🔍 测试2: 测试验证码获取API');
  
  const testEmail = '<EMAIL>';
  
  try {
    // 首先触发邮件检查
    console.log('📧 触发邮件检查...');
    const checkResponse = await fetch(`http://localhost:3000/check-email/${encodeURIComponent(testEmail)}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    
    const checkResult = await checkResponse.json();
    console.log('📧 邮件检查结果:', checkResult);
    
    // 等待一段时间后获取验证码
    console.log('⏳ 等待3秒后获取验证码...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const codeResponse = await fetch(`http://localhost:3000/verification-code/${encodeURIComponent(testEmail)}`);
    const codeResult = await codeResponse.json();
    
    console.log('🔢 验证码获取结果:', codeResult);
    
    if (codeResult.success && codeResult.code) {
      console.log('✅ 验证码获取成功:', codeResult.code);
      return true;
    } else {
      console.log('❌ 验证码获取失败:', codeResult.message || '无消息');
      return false;
    }
  } catch (error) {
    console.log('❌ 验证码API测试失败:', error.message);
    return false;
  }
}

// 测试3: 检查邮件列表
async function testEmailList() {
  console.log('\n🔍 测试3: 检查邮件列表');
  
  const testEmail = '<EMAIL>';
  
  try {
    const response = await fetch(`http://localhost:3000/emails/${encodeURIComponent(testEmail)}`);
    const result = await response.json();
    
    console.log('📧 邮件列表结果:', result);
    
    if (result.success && result.emails) {
      console.log(`✅ 找到 ${result.emails.length} 封邮件`);
      
      // 显示最近的几封邮件
      const recentEmails = result.emails.slice(0, 3);
      recentEmails.forEach((email, index) => {
        console.log(`📨 邮件 ${index + 1}:`);
        console.log(`   主题: ${email.subject}`);
        console.log(`   发件人: ${email.from}`);
        console.log(`   时间: ${email.date}`);
        console.log(`   内容预览: ${email.text ? email.text.substring(0, 100) + '...' : '无文本内容'}`);
      });
      
      return true;
    } else {
      console.log('❌ 获取邮件列表失败:', result.message || '无消息');
      return false;
    }
  } catch (error) {
    console.log('❌ 邮件列表测试失败:', error.message);
    return false;
  }
}

// 测试4: 模拟content.js中的验证码获取流程
async function testContentScriptFlow() {
  console.log('\n🔍 测试4: 模拟content.js中的验证码获取流程');
  
  const testEmail = '<EMAIL>';
  const maxRetries = 3;
  const checkInterval = 3000;
  
  console.log(`📧 测试邮箱: ${testEmail}`);
  console.log(`🔄 最大重试次数: ${maxRetries}`);
  console.log(`⏰ 检查间隔: ${checkInterval}ms`);
  
  // 首先触发邮件检查
  try {
    console.log('📧 步骤1: 触发服务器邮件检查...');
    const triggerResponse = await fetch(`http://localhost:3000/check-email/${encodeURIComponent(testEmail)}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    
    const triggerResult = await triggerResponse.json();
    console.log('📧 邮件检查触发结果:', triggerResult);
  } catch (error) {
    console.log('❌ 触发邮件检查失败:', error.message);
  }
  
  // 轮询获取验证码
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    console.log(`\n🔍 步骤2.${attempt + 1}: 尝试获取验证码 (${attempt + 1}/${maxRetries})`);
    
    try {
      const response = await fetch(`http://localhost:3000/verification-code/${encodeURIComponent(testEmail)}`);
      const result = await response.json();
      
      console.log('📊 API响应:', result);
      
      if (result.success && result.code) {
        console.log(`✅ 验证码获取成功: ${result.code}`);
        console.log(`📧 邮件时间戳: ${result.emailTimestamp}`);
        console.log(`🎯 相关性评分: ${result.relevanceScore}`);
        return true;
      } else {
        console.log(`❌ 第${attempt + 1}次尝试失败:`, result.message || '无消息');
        
        if (attempt < maxRetries - 1) {
          console.log(`⏳ 等待 ${checkInterval}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
      }
    } catch (error) {
      console.log(`❌ 第${attempt + 1}次尝试出错:`, error.message);
      
      if (attempt < maxRetries - 1) {
        console.log(`⏳ 等待 ${checkInterval}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }
  }
  
  console.log('❌ 所有尝试都失败了');
  return false;
}

// 测试5: 检查服务器统计信息
async function testServerStats() {
  console.log('\n🔍 测试5: 检查服务器统计信息');
  
  try {
    const response = await fetch('http://localhost:3000/stats');
    const result = await response.json();
    
    console.log('📊 服务器统计信息:');
    console.log('   总请求数:', result.totalRequests);
    console.log('   成功提取数:', result.successfulExtractions);
    console.log('   失败提取数:', result.failedExtractions);
    console.log('   删除邮件数:', result.deletedEmails);
    console.log('   缓存验证码数:', result.cachedCodes);
    console.log('   活跃请求数:', result.activeRequests);
    
    return true;
  } catch (error) {
    console.log('❌ 获取服务器统计失败:', error.message);
    return false;
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行验证码获取调试测试...\n');
  
  const results = [];
  
  // 按顺序运行测试
  results.push(await testImapServerConnection());
  results.push(await testVerificationCodeAPI());
  results.push(await testEmailList());
  results.push(await testContentScriptFlow());
  results.push(await testServerStats());
  
  const passedCount = results.filter(r => r).length;
  const totalCount = results.length;
  
  console.log('\n📋 测试结果总结:');
  console.log(`✅ 通过: ${passedCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - passedCount}/${totalCount}`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有测试通过！验证码获取系统正常工作。');
  } else {
    console.log('⚠️ 部分测试失败，需要进一步调试。');
    
    // 提供调试建议
    console.log('\n🔧 调试建议:');
    console.log('1. 确保IMAP服务器正在运行 (npm start)');
    console.log('2. 检查邮箱配置是否正确');
    console.log('3. 确认测试邮箱中有Microsoft验证邮件');
    console.log('4. 检查网络连接和防火墙设置');
    console.log('5. 查看服务器日志获取更多信息');
  }
  
  return passedCount === totalCount;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  // 需要fetch polyfill
  let fetch;
  try {
    fetch = require('node-fetch');
  } catch (error) {
    // 如果node-fetch不可用，使用内置的https模块
    const https = require('https');
    const http = require('http');
    const { URL } = require('url');

    fetch = function(url, options = {}) {
      return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;

        const req = client.request({
          hostname: urlObj.hostname,
          port: urlObj.port,
          path: urlObj.pathname + urlObj.search,
          method: options.method || 'GET',
          headers: options.headers || {}
        }, (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => {
            resolve({
              ok: res.statusCode >= 200 && res.statusCode < 300,
              status: res.statusCode,
              json: () => Promise.resolve(JSON.parse(data)),
              text: () => Promise.resolve(data)
            });
          });
        });

        req.on('error', reject);

        if (options.body) {
          req.write(options.body);
        }

        req.end();
      });
    };
  }

  global.fetch = fetch;

  module.exports = {
    testImapServerConnection,
    testVerificationCodeAPI,
    testEmailList,
    testContentScriptFlow,
    testServerStats,
    runAllTests
  };

  // 自动运行测试
  runAllTests().catch(console.error);
} else {
  // 浏览器环境
  runAllTests().catch(console.error);
}
