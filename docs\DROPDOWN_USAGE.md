# 下拉菜单处理使用说明

## 问题描述

在Microsoft注册页面的生日填写环节，年份可以直接通过修改`value`属性来填入，但是月份和日期使用的是下拉菜单组件，需要模拟用户的点击和选择操作。

## 解决方案

### 1. 主要改进

- **通用下拉框选择函数**: 创建了`selectDropdownOption()`函数，可以处理各种类型的下拉框
- **多种选择器支持**: 支持多种选择器模式，提高兼容性
- **文本内容匹配**: 当标准选择器失败时，通过文本内容查找选项
- **详细调试信息**: 提供完整的调试输出，便于问题排查

### 2. 核心函数

```javascript
// 通用下拉框选择函数
function selectDropdownOption(dropdownSelector, targetValue, optionType = 'month') {
  // 返回Promise，支持异步操作
  // dropdownSelector: 下拉框按钮的选择器
  // targetValue: 要选择的值（如月份数字或日期数字）
  // optionType: 选项类型（'month' 或 'day'）
}
```

### 3. 使用方式

#### 在content.js中的自动化使用：
```javascript
// 选择月份
await selectDropdownOption('button[name="BirthMonth"]', randomMonth, 'month');

// 选择日期
await selectDropdownOption('button[name="BirthDay"]', randomDay, 'day');
```

#### 手动调试使用：
```javascript
// 在浏览器控制台中
debugDropdown.quickSelect(8, 20); // 选择8月20日
```

### 4. 调试工具

#### 调试脚本文件：
- `debug-dropdown.js` - 完整的调试脚本
- `test-dropdown.bat` - 快速启动调试的批处理文件

#### 可用的调试函数：
- `debugDropdown.analyze()` - 分析页面下拉框结构
- `debugDropdown.testMonth(月份)` - 测试月份选择
- `debugDropdown.testDay(日期)` - 测试日期选择
- `debugDropdown.runFullTest()` - 运行完整测试
- `debugDropdown.quickSelect(月份, 日期)` - 快速选择生日

### 5. 使用步骤

1. **自动化使用**：
   - 代码已集成到`content.js`中
   - 在Microsoft注册页面会自动执行

2. **手动调试**：
   ```bash
   # 运行批处理文件
   test-dropdown.bat
   
   # 或者直接在浏览器控制台中粘贴debug-dropdown.js的内容
   ```

3. **测试流程**：
   - 打开Microsoft注册页面
   - 导航到生日填写页面
   - 打开浏览器开发者工具(F12)
   - 运行调试函数进行测试

### 6. 支持的选择器模式

函数会按以下顺序尝试查找选项：

1. `[role="option"][data-value="${targetValue}"]`
2. `[role="option"]:nth-child(${targetValue})`
3. `[role="listbox"] [role="option"]:nth-child(${targetValue})`
4. `[data-testid="${optionType}-${targetValue}"]`
5. `[aria-label*="${targetValue}月/日"]`
6. `[title*="${targetValue}月/日"]`
7. 文本内容匹配（如"8月"、"20日"等）

### 7. 错误处理

- 如果找不到下拉框，会输出错误信息
- 如果找不到目标选项，会输出所有可用选项的调试信息
- 使用Promise机制，支持错误捕获和处理

### 8. 注意事项

- 下拉框需要先点击展开，再选择选项
- 不同的网站可能使用不同的HTML结构，需要根据实际情况调整选择器
- 建议先使用调试工具测试，确认选择器正确后再集成到自动化脚本中

## 示例用法

```javascript
// 示例1: 选择8月20日
debugDropdown.quickSelect(8, 20);

// 示例2: 分析页面结构
debugDropdown.analyze();

// 示例3: 测试特定月份
debugDropdown.testMonth(6);

// 示例4: 测试特定日期
debugDropdown.testDay(15);
```

## 日期选择卡住问题解决方案

### 问题现象
- 月份可以正常选择
- 日期选择时卡住，无法完成选择
- 日期下拉框点击后没有选项出现

### 解决方案

#### 1. 使用改进的顺序选择函数
代码已更新为使用 `selectBirthDateSequentially()` 函数：
- 增加了更长的等待时间
- 改进了错误处理
- 添加了备用选择机制

#### 2. 专门的调试工具
创建了专门的日期选择调试脚本：

```javascript
// 加载 debug-day-selection.js 后使用
dayDebug.runFull(15);  // 完整调试流程
dayDebug.simpleSelect(15);  // 简化选择
```

#### 3. 强制修复工具
创建了修复卡住状态的脚本：

```javascript
// 加载 fix-day-stuck.js 后使用
fixDayStuck.runComplete(8, 15);  // 完整修复流程
fixDayStuck.checkAndFix();  // 检查并修复
```

### 调试步骤

1. **基础检查**：
   ```javascript
   dayDebug.analyze();  // 分析日期下拉框
   dayDebug.analyzeMonth();  // 检查月份状态
   ```

2. **测试点击**：
   ```javascript
   dayDebug.testClick();  // 测试下拉框点击
   ```

3. **强制修复**：
   ```javascript
   fixDayStuck.reset();  // 重置状态
   fixDayStuck.forceSelect(15);  // 强制选择
   ```

### 可用的调试脚本

1. **debug-day-selection.js** - 详细调试
2. **fix-day-stuck.js** - 快速修复
3. **test-dropdown.bat** - 启动调试环境

## 故障排除

### 一般问题
如果下拉框选择失败：

1. 检查页面是否已完全加载
2. 确认下拉框元素存在
3. 查看控制台输出的调试信息
4. 尝试手动点击下拉框，观察选项结构
5. 根据实际HTML结构调整选择器

### 日期选择卡住问题
如果日期选择卡住：

1. **立即修复**：
   ```javascript
   fixDayStuck.runComplete(8, 15);
   ```

2. **详细调试**：
   ```javascript
   dayDebug.runFull(15);
   ```

3. **检查月份依赖**：
   - 确保月份已正确选择
   - 某些实现中日期选项依赖于月份

4. **增加等待时间**：
   - 月份选择后等待更长时间
   - 确保DOM更新完成

5. **使用备用方案**：
   - 如果目标日期不可用，自动选择最接近的日期
   - 避免选择超出月份范围的日期
