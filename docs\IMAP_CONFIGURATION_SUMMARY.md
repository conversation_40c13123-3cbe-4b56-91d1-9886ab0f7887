# RTBS真实IMAP服务配置总结

## 🎯 配置完成清单

我已经为您创建了完整的真实IMAP服务系统，以下是所有相关文件和配置步骤：

## 📁 新增文件列表

### 核心服务文件
- `imap-server.js` - 主要的IMAP邮件服务器
- `package.json` - Node.js项目配置和依赖
- `test-imap.js` - IMAP服务器测试脚本

### 配置文件
- `imap-config.json` - IMAP服务器配置
- `.env.example` - 环境变量配置示例

### 安装脚本
- `install-imap-server.bat` - Windows自动安装脚本
- `start-imap-server.bat` - Windows启动脚本

### 文档
- `IMAP_SETUP_GUIDE.md` - 详细安装配置指南

## 🚀 快速配置步骤

### 第1步：安装Node.js环境
1. 下载并安装Node.js 14.0+：https://nodejs.org/
2. 验证安装：打开命令行运行 `node --version`

### 第2步：配置邮箱服务
编辑 `imap-config.json` 文件，填入真实配置：
```json
{
  "user": "<EMAIL>",
  "password": "您的真实邮箱密码",
  "host": "imap.s4464.cfd",
  "port": 993,
  "tls": true,
  "tlsOptions": {
    "rejectUnauthorized": false
  }
}
```

### 第3步：运行安装脚本
双击运行 `install-imap-server.bat`，脚本会自动：
- 检查Node.js环境
- 安装所需依赖包
- 验证配置文件
- 测试服务器启动

### 第4步：启动IMAP服务
双击运行 `start-imap-server.bat` 启动服务器

### 第5步：验证服务
打开浏览器访问：http://localhost:3000/health

## 🔧 邮箱服务器配置要求

### 必需配置
1. **域名邮箱服务**：s4464.cfd域名必须配置邮件服务
2. **IMAP服务**：启用IMAP协议，端口993
3. **SSL/TLS**：启用加密连接
4. **邮箱账号**：创建*****************邮箱

### 推荐的邮件服务提供商
- **cPanel主机**：大多数支持IMAP
- **Google Workspace**：企业邮箱服务
- **Microsoft 365**：企业邮箱服务
- **自建邮件服务器**：如Postfix + Dovecot

## 📊 API接口说明

启动服务后，以下API将可用：

### 1. 健康检查
```
GET http://localhost:3000/health
```

### 2. 获取验证码（扩展使用）
```
GET http://localhost:3000/verification-code/账号@s4464.cfd
```

### 3. 手动检查邮件
```
POST http://localhost:3000/check-email/账号@s4464.cfd
```

### 4. 获取邮件列表
```
GET http://localhost:3000/emails/账号@s4464.cfd
```

## 🔄 扩展集成

我已经更新了 `content.js` 文件，扩展现在会：
1. 自动连接到本地IMAP服务器（http://localhost:3000）
2. 触发邮件检查
3. 获取验证码
4. 自动填写到注册表单

## 🧪 测试流程

### 自动测试
```bash
npm test
```

### 手动测试
1. 启动IMAP服务器
2. 发送包含6位验证码的邮件到*****************
3. 调用API：`GET http://localhost:3000/verification-code/<EMAIL>`
4. 验证返回的验证码是否正确

## 🚨 重要注意事项

### 安全配置
- **密码安全**：不要在代码中硬编码密码
- **网络安全**：仅在本地运行，不要暴露到公网
- **访问控制**：限制邮箱访问权限

### 邮件服务要求
- **域名配置**：确保s4464.cfd域名指向您的邮件服务器
- **MX记录**：正确配置DNS MX记录
- **SSL证书**：配置有效的SSL证书
- **防火墙**：开放IMAP端口993

### 故障排除
- **连接失败**：检查IMAP服务器地址和端口
- **认证失败**：验证邮箱账号和密码
- **验证码提取失败**：检查邮件内容格式
- **服务器启动失败**：检查端口占用情况

## 📋 生产环境建议

### 1. 使用环境变量
创建 `.env` 文件：
```env
IMAP_HOST=imap.s4464.cfd
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-secure-password
SERVER_PORT=3000
```

### 2. 使用进程管理器
```bash
npm install -g pm2
pm2 start imap-server.js --name rtbs-imap
pm2 startup
pm2 save
```

### 3. 监控和日志
- 配置日志轮转
- 设置错误监控
- 定期备份配置

## ✅ 配置验证清单

部署前请确认：

- [ ] Node.js 14.0+ 已安装
- [ ] s4464.cfd域名邮件服务已配置
- [ ] *****************邮箱已创建
- [ ] IMAP服务器可访问（imap.s4464.cfd:993）
- [ ] 邮箱密码已正确配置
- [ ] 依赖包已安装（npm install）
- [ ] 服务器可正常启动
- [ ] 健康检查API响应正常
- [ ] 测试邮件可正常接收
- [ ] 验证码提取功能正常

## 🎉 完成后的效果

配置完成后，您的RTBS扩展将能够：

1. **自动生成**Microsoft账号邮箱
2. **自动填写**注册表单
3. **自动接收**验证码邮件
4. **自动提取**6位验证码
5. **自动填写**验证码到表单
6. **完成整个**注册流程

整个过程完全自动化，无需人工干预！

## 📞 技术支持

如果在配置过程中遇到问题：

1. **查看日志**：检查服务器控制台输出
2. **运行测试**：使用 `npm test` 验证配置
3. **检查网络**：确认IMAP服务器可访问
4. **验证配置**：确认邮箱账号和密码正确

配置完成后，您就拥有了一个完全自动化的Microsoft账号注册系统！🚀
