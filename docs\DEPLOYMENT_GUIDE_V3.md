# Microsoft注册系统 v3.0 部署指南

## 🎯 概述

Microsoft注册系统 v3.0 是完全重写的微软账号自动注册系统，基于状态机的阶段化处理架构，提供更可靠、更智能的注册流程管理。

## 📋 系统要求

### 基础要求
- Chrome/Edge 浏览器 (版本 88+)
- Node.js 14+ (用于IMAP服务器)
- 稳定的网络连接
- s4464.cfd 域名邮件服务配置

### 新增要求 (v3.0)
- 支持ES6+ 语法的浏览器环境
- 更大的内存空间 (推荐 4GB+)
- 更稳定的IMAP服务器连接

## 🚀 部署步骤

### 1. 文件结构检查

确保以下文件已正确放置：

```
RTBS/
├── manifest.json
├── content.js (已更新支持v3)
├── popup.html (已更新UI)
├── popup.js (已更新状态显示)
├── background.js
├── microsoft-registration-state-machine.js (新增)
├── microsoft-registration-handlers.js (新增)
├── microsoft-registration-controller.js (新增)
├── page-detector.js (新增)
├── remaining-stage-handlers.js (新增)
├── microsoft-registration-v3.js (新增)
├── enhanced-imap-service.js (新增)
├── error-handling-system.js (新增)
└── imap-server.js (已更新)
```

### 2. 更新 manifest.json

确保 manifest.json 包含所有新文件：

```json
{
  "content_scripts": [
    {
      "matches": ["*://*.microsoft.com/*", "*://*.live.com/*", "*://*.outlook.com/*"],
      "js": [
        "microsoft-registration-state-machine.js",
        "microsoft-registration-handlers.js",
        "remaining-stage-handlers.js",
        "page-detector.js",
        "microsoft-registration-controller.js",
        "error-handling-system.js",
        "microsoft-registration-v3.js",
        "content.js"
      ],
      "run_at": "document_end"
    }
  ]
}
```

### 3. IMAP服务器配置

#### 3.1 安装依赖
```bash
cd RTBS
npm install imap mailparser express
```

#### 3.2 配置环境变量
创建 `.env` 文件：
```env
IMAP_HOST=imap.s4464.cfd
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-password-here
IMAP_TLS=true
SERVER_PORT=3000
```

#### 3.3 启动IMAP服务器
```bash
node imap-server.js
```

### 4. 浏览器扩展安装

1. 打开 Chrome/Edge 浏览器
2. 进入扩展管理页面 (`chrome://extensions/`)
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 RTBS 文件夹

## 🧪 测试流程

### 1. 系统初始化测试

#### 1.1 检查v3系统加载
1. 打开 Microsoft 注册页面
2. 按 F12 打开开发者工具
3. 在 Console 中查看是否有以下日志：
   ```
   ✅ Microsoft注册系统 v3.0 已加载
   🔧 创建Microsoft注册系统 v3.0...
   ✅ Microsoft注册系统初始化完成
   ```

#### 1.2 检查状态机初始化
在 Console 中执行：
```javascript
// 检查v3系统是否可用
console.log('v3系统:', window.getMicrosoftRegistrationSystemV3());

// 检查状态机
const system = window.getMicrosoftRegistrationSystemV3();
console.log('状态机状态:', system.getSystemStatus());
```

### 2. 页面检测测试

#### 2.1 测试页面检测器
在不同的注册页面执行：
```javascript
const detector = new PageDetector();
const stage = detector.detectCurrentStage();
console.log('检测到的阶段:', stage);
```

#### 2.2 验证检测准确性
- 数据许可页面 → 应检测为 `data_permission`
- 邮箱输入页面 → 应检测为 `email_input`
- 验证码页面 → 应检测为 `verification_code`
- 个人信息页面 → 应检测为 `personal_info`

### 3. IMAP服务测试

#### 3.1 服务器连接测试
```bash
curl http://localhost:3000/health
```
应返回：
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### 3.2 邮件检查测试
```bash
curl -X POST http://localhost:3000/check-email/<EMAIL>
```

#### 3.3 验证码获取测试
```bash
curl http://localhost:3000/verification-code/<EMAIL>
```

### 4. 完整注册流程测试

#### 4.1 启动注册流程
1. 访问 Microsoft 注册页面
2. 打开扩展 popup
3. 确认显示 v3.0 版本标识
4. 观察状态变化

#### 4.2 阶段转换测试
监控每个阶段的转换：
```javascript
// 在 Console 中监听状态变化
window.addEventListener('microsoftRegistrationSystemReady', (event) => {
  const system = event.detail.system;
  system.controller.stateMachine.addListener((event, data) => {
    console.log('状态变化:', event, data);
  });
});
```

#### 4.3 错误处理测试
故意触发错误并观察恢复机制：
```javascript
// 模拟网络错误
const system = window.getMicrosoftRegistrationSystemV3();
system.controller.stateMachine.handleError(new Error('测试网络错误'));
```

## 🔍 故障排除

### 常见问题

#### 1. v3系统未加载
**症状**: Console 显示 "Microsoft注册系统 v3.0 未加载"
**解决方案**:
- 检查文件加载顺序
- 确认所有 v3 文件都已包含在 manifest.json 中
- 清除浏览器缓存并重新加载扩展

#### 2. 状态机初始化失败
**症状**: 系统状态显示为 'error'
**解决方案**:
- 检查依赖类是否正确加载
- 查看 Console 错误日志
- 重新初始化系统

#### 3. IMAP服务连接失败
**症状**: 验证码获取失败
**解决方案**:
- 检查 IMAP 服务器是否运行
- 验证网络连接
- 检查邮箱配置

#### 4. 页面检测不准确
**症状**: 阶段检测错误或延迟
**解决方案**:
- 检查页面元素是否变化
- 更新检测规则
- 增加检测权重

### 调试工具

#### 1. 系统状态检查
```javascript
// 获取完整系统状态
const system = window.getMicrosoftRegistrationSystemV3();
console.log('系统信息:', system.getSystemInfo());
console.log('系统状态:', system.getSystemStatus());
```

#### 2. 错误历史查看
```javascript
// 查看错误统计
const system = window.getMicrosoftRegistrationSystemV3();
if (system.controller && system.controller.errorHandler) {
  console.log('错误统计:', system.controller.errorHandler.getErrorStatistics());
}
```

#### 3. 页面检测历史
```javascript
// 查看检测历史
const detector = new PageDetector();
console.log('检测历史:', detector.getDetectionHistory());
```

## 📊 性能监控

### 关键指标

1. **系统初始化时间**: 应在 2 秒内完成
2. **页面检测响应时间**: 应在 1 秒内完成
3. **状态转换时间**: 应在 3 秒内完成
4. **IMAP响应时间**: 应在 10 秒内获取验证码
5. **内存使用**: 应控制在 50MB 以内

### 监控方法

```javascript
// 性能监控
const system = window.getMicrosoftRegistrationSystemV3();
const startTime = performance.now();

system.controller.stateMachine.addListener((event, data) => {
  const endTime = performance.now();
  console.log(`${event} 耗时: ${endTime - startTime}ms`);
});
```

## 🔄 版本兼容性

### v2.x 兼容模式
- 如果 v3 系统加载失败，自动回退到 v2.x 兼容模式
- 保持原有 popup 界面显示
- 维持基本的注册功能

### 迁移策略
- 逐步迁移现有功能到 v3 架构
- 保留 v2.x 代码作为后备
- 提供平滑的升级路径

## 📝 部署检查清单

- [ ] 所有 v3 文件已正确放置
- [ ] manifest.json 已更新
- [ ] IMAP 服务器已配置并运行
- [ ] 浏览器扩展已重新加载
- [ ] 系统初始化测试通过
- [ ] 页面检测测试通过
- [ ] IMAP 服务测试通过
- [ ] 完整注册流程测试通过
- [ ] 错误处理测试通过
- [ ] 性能指标符合要求

## 🎉 部署完成

恭喜！Microsoft注册系统 v3.0 已成功部署。系统现在具备：

- ✅ 基于状态机的严格阶段控制
- ✅ 智能页面检测和表单处理
- ✅ 可靠的IMAP邮件服务
- ✅ 智能错误处理和恢复
- ✅ 增强的用户界面和状态显示
- ✅ 完整的资源管理和清理

系统已准备好处理Microsoft账号自动注册任务！
