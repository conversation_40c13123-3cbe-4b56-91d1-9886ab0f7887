/**
 * 测试脚本：设置测试账号状态
 * 用于在验证码页面快速设置账号信息以便测试
 */

// 生成测试账号
function generateTestAccount() {
  const timestamp = Date.now().toString().slice(-6);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `test${timestamp}${randomStr}@outlook.com`;
}

// 设置测试账号状态
function setTestAccountForVerification() {
  const testAccount = generateTestAccount();
  
  console.log('🧪 设置测试账号状态...');
  console.log('📧 测试账号:', testAccount);
  
  // 设置到Chrome存储
  chrome.storage.local.set({
    'microsoft_registration_status': {
      currentAccount: testAccount,
      currentStep: 'verification_code',
      currentStageIndex: 2, // verification_code在序列中的索引
      stageCompleted: {
        'data_permission': true,
        'signup': true
      },
      attempts: 1,
      verificationStatus: '等待验证码...',
      registrationComplete: false,
      timestamp: new Date().toISOString()
    }
  }, () => {
    console.log('✅ 测试状态已设置到存储');
    
    // 如果在content script环境中，直接更新状态
    if (typeof msRegistrationState !== 'undefined') {
      msRegistrationState.currentAccount = testAccount;
      msRegistrationState.currentStep = 'verification_code';
      msRegistrationState.currentStageIndex = 2;
      msRegistrationState.stageCompleted = {
        'data_permission': true,
        'signup': true
      };
      msRegistrationState.attempts = 1;
      msRegistrationState.verificationStatus = '等待验证码...';
      msRegistrationState.registrationComplete = false;
      
      // 更新IMAP服务
      if (typeof imapService !== 'undefined') {
        imapService.currentEmail = testAccount;
        console.log('📧 IMAP服务邮箱已更新为:', testAccount);
      }
      
      console.log('✅ 内存状态已更新');
      console.log('📊 当前状态:', {
        currentAccount: msRegistrationState.currentAccount,
        currentStep: msRegistrationState.currentStep,
        currentStageIndex: msRegistrationState.currentStageIndex
      });
    }
  });
  
  return testAccount;
}

// 清除测试状态
function clearTestAccount() {
  console.log('🧹 清除测试状态...');
  
  chrome.storage.local.remove('microsoft_registration_status', () => {
    console.log('✅ 存储状态已清除');
    
    // 如果在content script环境中，重置状态
    if (typeof msRegistrationState !== 'undefined') {
      msRegistrationState.currentAccount = null;
      msRegistrationState.currentStep = 'none';
      msRegistrationState.currentStageIndex = -1;
      msRegistrationState.stageCompleted = {};
      msRegistrationState.attempts = 0;
      msRegistrationState.verificationStatus = '等待中';
      msRegistrationState.registrationComplete = false;
      
      if (typeof imapService !== 'undefined') {
        imapService.currentEmail = null;
      }
      
      console.log('✅ 内存状态已重置');
    }
  });
}

// 手动触发验证码处理
function triggerVerificationCodeHandling() {
  console.log('🎯 手动触发验证码处理...');
  
  if (typeof handleVerificationCodePage !== 'undefined') {
    handleVerificationCodePage();
    console.log('✅ 验证码处理已触发');
  } else {
    console.error('❌ handleVerificationCodePage函数不存在');
  }
}

// 检查当前状态
function checkCurrentState() {
  console.log('📊 检查当前状态...');
  
  // 检查存储状态
  chrome.storage.local.get('microsoft_registration_status', (result) => {
    console.log('💾 存储状态:', result.microsoft_registration_status);
  });
  
  // 检查内存状态
  if (typeof msRegistrationState !== 'undefined') {
    console.log('🧠 内存状态:', {
      currentAccount: msRegistrationState.currentAccount,
      currentStep: msRegistrationState.currentStep,
      currentStageIndex: msRegistrationState.currentStageIndex,
      verificationStatus: msRegistrationState.verificationStatus,
      isProcessing: msRegistrationState.isProcessing
    });
  }
  
  // 检查IMAP状态
  if (typeof imapService !== 'undefined') {
    console.log('📧 IMAP状态:', {
      currentEmail: imapService.currentEmail,
      isWaitingForCode: imapService.isWaitingForCode,
      retryCount: imapService.retryCount
    });
  }
  
  // 检查页面状态
  console.log('🌐 页面状态:', {
    url: window.location.href,
    hasCodeInputs: document.querySelectorAll('[id*="codeEntry"]').length > 0,
    hasSubmitButton: !!document.querySelector('button[type="submit"]')
  });
}

// 模拟收到验证码
function simulateVerificationCode(code = '123456') {
  console.log(`📧 模拟收到验证码: ${code}`);
  
  // 查找验证码输入框
  const codeInputs = [];
  for (let i = 0; i < 6; i++) {
    const input = document.querySelector(`#codeEntry-${i}`);
    if (input) {
      codeInputs.push(input);
    }
  }
  
  if (codeInputs.length === 6) {
    // 填入验证码
    for (let i = 0; i < 6; i++) {
      codeInputs[i].value = code[i];
      codeInputs[i].dispatchEvent(new Event('input', { bubbles: true }));
      codeInputs[i].dispatchEvent(new Event('change', { bubbles: true }));
    }
    
    console.log('✅ 验证码已填入输入框');
    
    // 尝试提交
    setTimeout(() => {
      const submitButton = document.querySelector('button[type="submit"]');
      if (submitButton && !submitButton.disabled) {
        console.log('🎯 自动提交验证码');
        submitButton.click();
      } else {
        console.log('⚠️ 提交按钮不可用，请手动提交');
      }
    }, 500);
  } else {
    console.error(`❌ 找到 ${codeInputs.length} 个输入框，期望6个`);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.testAccount = {
    set: setTestAccountForVerification,
    clear: clearTestAccount,
    check: checkCurrentState,
    trigger: triggerVerificationCodeHandling,
    simulate: simulateVerificationCode,
    generate: generateTestAccount
  };
  
  console.log('🧪 测试账号工具已加载');
  console.log('使用方法:');
  console.log('  testAccount.set() - 设置测试账号');
  console.log('  testAccount.clear() - 清除测试状态');
  console.log('  testAccount.check() - 检查当前状态');
  console.log('  testAccount.trigger() - 手动触发验证码处理');
  console.log('  testAccount.simulate("123456") - 模拟填入验证码');
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    setTestAccountForVerification,
    clearTestAccount,
    checkCurrentState,
    triggerVerificationCodeHandling,
    simulateVerificationCode,
    generateTestAccount
  };
}
