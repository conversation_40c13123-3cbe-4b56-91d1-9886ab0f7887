# Microsoft账号自动注册功能使用指南

## 🎯 功能概述

RTBS扩展现已集成Microsoft账号自动注册功能，可以自动完成以下流程：

1. **账号生成** - 自动生成12位随机字符的邮箱账号（格式：随机字符@s4464.cfd）
2. **邮箱填写** - 在注册页面自动填写生成的邮箱地址
3. **验证码处理** - 通过IMAP服务接收并自动填写6位验证码
4. **个人信息填写** - 自动填写随机生成的生日信息
5. **姓名信息填写** - 自动填写随机生成的姓名
6. **人机验证** - 自动处理人机验证（长按操作）
7. **登录完成** - 自动跳过额外设置，导航到奖励页面
8. **文件保存** - 在AutoRL文件夹中保存账号信息

## 🚀 使用方法

### 1. 启动注册流程
访问Microsoft注册页面：
```
https://signup.live.com/signup?cobrandid=03c8bbb5-2dff-4721-8261-a4ccff24c81a&contextid=24CAE7F75BFB5E1B&opid=1DDAB8E653226068&bk=1753579027&sru=https://login.live.com/oauth20_authorize.srf%3fclient_id%3d9c941f7c-a811-4e9c-8e66-29fdec50490f%26cobrandid%3d03c8bbb5-2dff-4721-8261-a4ccff24c81a%26client_id%3d9c941f7c-a811-4e9c-8e66-29fdec50490f%26cobrandid%3d03c8bbb5-2dff-4721-8261-a4ccff24c81a%26contextid%3d24CAE7F75BFB5E1B%26opid%3d1DDAB8E653226068%26mkt%3dZH-CN%26lc%3d2052%26bk%3d1753579027%26uaid%3d04a7c5ab27b34de9b18ad72294d6ac06&lw=easi2&fl=1&uiflavor=web&fluent=2&client_id=00000000407BC851&lic=1&mkt=ZH-CN&lc=2052&uaid=04a7c5ab27b34de9b18ad72294d6ac06
```

### 2. 监控注册进度
点击扩展图标查看Microsoft注册面板，实时监控：
- **账号** - 当前生成的邮箱账号
- **步骤** - 当前注册步骤
- **验证** - 验证状态
- **状态** - 整体注册状态

### 3. 查看保存的账号
注册完成后，账号信息将保存到：
```
Downloads\RTBS\AutoRL\[账号名].txt
```

## 📋 注册流程详解

### 步骤1：邮箱填写
- 自动生成12位随机字符邮箱（如：<EMAIL>）
- 自动填写到邮箱输入框
- 自动点击"下一个"按钮

### 步骤2：验证码处理
- 启动IMAP邮件检查服务
- 从邮件中提取6位安全代码
- 自动填写到6个验证码输入框
- 自动提交验证码

### 步骤3：个人信息填写
- **年份**：随机选择1995-2005年
- **月份**：随机选择1-12月
- **日期**：随机选择1-26日
- 自动点击"下一个"按钮

### 步骤4：姓名信息填写
- **姓氏**：1-4位随机小写字母
- **名字**：2-4位随机小写字母+数字
- 自动点击"下一个"按钮

### 步骤5：人机验证
- 检测"按住"提示
- 模拟3秒长按操作
- 处理验证失败重试（最多3次）
- 验证成功后继续

### 步骤6：登录完成
- 自动点击"暂时跳过"按钮
- 处理欢迎页面的各种弹窗
- 导航到Microsoft Rewards页面

### 步骤7：完成注册
- 点击奖励项目
- 保存账号信息到文件
- 标记注册完成

## ⚙️ 配置说明

### IMAP服务配置
目前使用模拟IMAP服务，实际部署时需要：
1. 配置真实的IMAP服务器
2. 设置s4464.cfd域名的邮件接收
3. 实现邮件内容解析

### 错误处理机制
- **自动重试**：失败时自动重试（最多3次）
- **状态保存**：实时保存注册状态到本地存储
- **错误日志**：详细的控制台日志记录
- **超时处理**：页面加载超时自动处理

## 🎨 界面说明

### Microsoft注册面板
在Microsoft注册相关页面时，扩展popup会显示专用的紫色主题面板：

- **账号行**：蓝色渐变，显示当前生成的账号
- **步骤行**：黄色渐变，显示当前注册步骤
- **验证行**：绿色渐变，显示验证状态
- **状态行**：紫色渐变，显示整体状态

### 状态指示器
- 🟢 **绿色**：注册进行中或已完成
- 🔴 **红色**：出现错误或失败

## 🔧 技术实现

### 核心模块
1. **账号生成器** - 生成随机邮箱账号
2. **页面检测器** - 识别不同的注册页面
3. **表单填写器** - 自动填写各种表单
4. **IMAP服务** - 邮件接收和验证码提取
5. **状态管理器** - 跟踪和保存注册状态
6. **错误处理器** - 处理异常和重试

### 文件结构
- `content.js` - 主要的注册逻辑
- `background.js` - 文件保存和消息处理
- `popup.html/js` - 状态显示界面

## 📝 注意事项

1. **网络环境**：确保网络连接稳定
2. **页面加载**：等待页面完全加载后再操作
3. **验证码时效**：验证码有时效性，需及时处理
4. **人机验证**：可能需要多次尝试才能通过
5. **文件权限**：确保有Downloads文件夹的写入权限

## 🐛 故障排除

### 常见问题
1. **验证码未收到** - 检查IMAP服务配置
2. **人机验证失败** - 会自动重试，最多3次
3. **页面加载超时** - 刷新页面重新开始
4. **文件保存失败** - 检查Downloads文件夹权限

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console标签页的日志
3. 检查扩展的popup状态显示
4. 查看本地存储中的状态数据

## 🔄 更新日志

### v1.1.0 (当前版本)
- ✅ 添加Microsoft账号自动注册功能
- ✅ 集成IMAP邮件服务
- ✅ 实现完整的注册流程自动化
- ✅ 添加状态监控和错误处理
- ✅ 新增Microsoft注册专用界面面板
